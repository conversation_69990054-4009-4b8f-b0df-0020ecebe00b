{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_BottomTabsNativeComponent", "_reactNative", "_flags", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "BottomTabs", "props", "console", "info", "onNativeFocusChange", "experimentalControlNavigationStateInJS", "featureFlags", "experiment", "controlledBottomTabs", "filteredProps", "componentNodeRef", "React", "useRef", "componentNodeHandle", "useEffect", "current", "findNodeHandle", "onNativeFocusChangeCallback", "useCallback", "event", "log", "JSON", "stringify", "nativeEvent", "createElement", "style", "styles", "fillParent", "controlNavigationStateInJS", "ref", "children", "_default", "StyleSheet", "create", "flex", "width", "height"], "sourceRoot": "../../../src", "sources": ["components/BottomTabs.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,YAAA,GAAAF,OAAA;AAQA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAoC,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAP,OAAA,EAAAO,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAAb,MAAA,CAAAc,MAAA,GAAAd,MAAA,CAAAc,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA;AAuCpC;AACA;AACA;AACA,SAASO,UAAUA,CAACC,KAAsB,EAAE;EAC1CC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;EAEjC,MAAM;IACJC,mBAAmB;IACnBC,sCAAsC,GAAGC,cAAY,CAACC,UAAU,CAC7DC,oBAAoB;IACvB,GAAGC;EACL,CAAC,GAAGR,KAAK;EAET,MAAMS,gBAAgB,GACpBC,cAAK,CAACC,MAAM,CAAkD,IAAI,CAAC;EACrE,MAAMC,mBAAmB,GAAGF,cAAK,CAACC,MAAM,CAAS,CAAC,CAAC,CAAC;EAEpDD,cAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAIJ,gBAAgB,CAACK,OAAO,IAAI,IAAI,EAAE;MACpCF,mBAAmB,CAACE,OAAO,GACzB,IAAAC,2BAAc,EAACN,gBAAgB,CAACK,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,MAAM;MACLF,mBAAmB,CAACE,OAAO,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,2BAA2B,GAAGN,cAAK,CAACO,WAAW,CAClDC,KAAmD,IAAK;IACvDjB,OAAO,CAACkB,GAAG,CACT,eACEP,mBAAmB,CAACE,OAAO,IAAI,CAAC,CAAC,0BACTM,IAAI,CAACC,SAAS,CAACH,KAAK,CAACI,WAAW,CAAC,EAC7D,CAAC;IACDnB,mBAAmB,GAAGe,KAAK,CAAC;EAC9B,CAAC,EACD,CAACf,mBAAmB,CACtB,CAAC;EAED,oBACEvB,MAAA,CAAAD,OAAA,CAAA4C,aAAA,CAACxC,0BAAA,CAAAJ,OAAyB,EAAAS,QAAA;IACxBoC,KAAK,EAAEC,MAAM,CAACC,UAAW;IACzBvB,mBAAmB,EAAEa,2BAA4B;IACjDW,0BAA0B,EAAEvB;IAC5B;IAAA;IACAwB,GAAG,EAAEnB;EAAiB,GAClBD,aAAa,GAChBA,aAAa,CAACqB,QACU,CAAC;AAEhC;AAAC,IAAAC,QAAA,GAAArD,OAAA,CAAAE,OAAA,GAEcoB,UAAU;AAEzB,MAAM0B,MAAM,GAAGM,uBAAU,CAACC,MAAM,CAAC;EAC/BN,UAAU,EAAE;IACVO,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}