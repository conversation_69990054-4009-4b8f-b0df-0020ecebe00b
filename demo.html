<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram + - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            margin: 20px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 20px;
            margin-bottom: 30px;
            opacity: 0.9;
            font-weight: 500;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }
        
        .security-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(0, 200, 81, 0.2);
            border: 1px solid #00C851;
            padding: 8px 16px;
            border-radius: 25px;
            margin: 20px 0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .security-badge::before {
            content: "🛡️";
            margin-right: 8px;
        }
        
        .download-section {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .download-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .download-btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .download-btn::before {
            margin-right: 8px;
            font-size: 20px;
        }
        
        .ios-btn::before {
            content: "🍎";
        }
        
        .android-btn::before {
            content: "🤖";
        }
        
        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #FFD700;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .tech-stack {
            margin-top: 30px;
            font-size: 12px;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .logo {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 18px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">Instagram +</div>
        <div class="subtitle">Instagram with Free Verification & Advanced Security</div>
        
        <div class="security-badge">
            MAXIMUM SECURITY LEVEL ACTIVE
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">✅</div>
                <div class="feature-title">Free Verification</div>
                <div class="feature-desc">Get your verification badge completely free - no payment required!</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">Advanced Security</div>
                <div class="feature-desc">Enterprise-grade security with biometric authentication and encryption</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Cross-Platform</div>
                <div class="feature-desc">Available on iOS, Android, and Web with seamless synchronization</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🛡️</div>
                <div class="feature-title">Privacy First</div>
                <div class="feature-desc">Your data is protected with end-to-end encryption and secure storage</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🌍</div>
                <div class="feature-title">Beautiful Design</div>
                <div class="feature-desc">African-inspired green and white theme with modern UI/UX</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Enhanced Features</div>
                <div class="feature-desc">All Instagram features plus advanced security and free verification</div>
            </div>
        </div>
        
        <div class="download-section">
            <h3>Download Instagram +</h3>
            <div class="download-buttons">
                <a href="#" class="download-btn ios-btn">Download for iOS</a>
                <a href="#" class="download-btn android-btn">Download for Android</a>
            </div>
        </div>
        
        <div class="status">
            <strong>🚀 Development Status:</strong> Ready for App Store deployment!<br>
            <strong>🔧 Built with:</strong> React Native + Expo + Advanced Security Services
        </div>
        
        <div class="tech-stack">
            <strong>Tech Stack:</strong> React Native • Expo • Supabase • Biometric Auth • End-to-End Encryption • Device Security • Real-time Monitoring
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            
            features.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.1}s`;
                feature.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .feature {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
