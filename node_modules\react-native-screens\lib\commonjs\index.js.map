{"version": 3, "names": ["require", "_types", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_core", "_Screen", "_interopRequireWildcard", "_ScreenStackHeaderConfig", "_SearchBar", "_interopRequireDefault", "_ScreenContainer", "_ScreenStack", "_ScreenStackItem", "_FullWindowOverlay", "_ScreenFooter", "_ScreenContentWrapper", "_utils", "_flags", "_useTransitionProgress", "_BottomTabs", "_BottomTabsScreen", "_ScreenStackHost", "_StackScreen", "_SplitViewHost", "_SplitViewScreen", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "i", "set"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEAA,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAAAE,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,MAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,MAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAKA,IAAAS,KAAA,GAAAd,OAAA;AAUA,IAAAe,OAAA,GAAAC,uBAAA,CAAAhB,OAAA;AAMA,IAAAiB,wBAAA,GAAAjB,OAAA;AAUA,IAAAkB,UAAA,GAAAC,sBAAA,CAAAnB,OAAA;AACA,IAAAoB,gBAAA,GAAAD,sBAAA,CAAAnB,OAAA;AACA,IAAAqB,YAAA,GAAAF,sBAAA,CAAAnB,OAAA;AACA,IAAAsB,gBAAA,GAAAH,sBAAA,CAAAnB,OAAA;AACA,IAAAuB,kBAAA,GAAAJ,sBAAA,CAAAnB,OAAA;AACA,IAAAwB,aAAA,GAAAL,sBAAA,CAAAnB,OAAA;AACA,IAAAyB,qBAAA,GAAAN,sBAAA,CAAAnB,OAAA;AAKA,IAAA0B,MAAA,GAAA1B,OAAA;AAQA,IAAA2B,MAAA,GAAA3B,OAAA;AAKA,IAAA4B,sBAAA,GAAAT,sBAAA,CAAAnB,OAAA;AAKA,IAAA6B,WAAA,GAAAV,sBAAA,CAAAnB,OAAA;AACA,IAAA8B,iBAAA,GAAAX,sBAAA,CAAAnB,OAAA;AACA,IAAA+B,gBAAA,GAAAZ,sBAAA,CAAAnB,OAAA;AACA,IAAAgC,YAAA,GAAAhB,uBAAA,CAAAhB,OAAA;AAIA,IAAAiC,cAAA,GAAAd,sBAAA,CAAAnB,OAAA;AACA,IAAAkC,gBAAA,GAAAf,sBAAA,CAAAnB,OAAA;AAAgF,SAAAmB,uBAAAgB,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAnB,wBAAAmB,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAA5B,GAAA,CAAAsB,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAA3C,MAAA,CAAAS,cAAA,IAAAT,MAAA,CAAA4C,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAxC,cAAA,CAAAC,IAAA,CAAA2B,CAAA,EAAAY,CAAA,SAAAC,CAAA,GAAAH,CAAA,GAAA3C,MAAA,CAAA4C,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAC,CAAA,KAAAA,CAAA,CAAAnC,GAAA,IAAAmC,CAAA,CAAAC,GAAA,IAAA/C,MAAA,CAAAS,cAAA,CAAAgC,CAAA,EAAAI,CAAA,EAAAC,CAAA,IAAAL,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAN,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAQ,GAAA,CAAAd,CAAA,EAAAQ,CAAA,GAAAA,CAAA", "ignoreList": []}