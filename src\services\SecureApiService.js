import SecurityService from './SecurityService';
import AuthService from './AuthService';

/**
 * Secure API Service for Africa Social
 * Implements secure communication with backend services
 * Features:
 * - Request/Response encryption
 * - API rate limiting
 * - Request signing
 * - CSRF protection
 * - Input validation
 * - Error handling
 */
class SecureApiService {
  constructor() {
    this.baseURL = 'https://your-api-endpoint.com/api';
    this.rateLimitWindow = 60 * 1000; // 1 minute
    this.maxRequestsPerWindow = 100;
    this.requestQueue = [];
    this.csrfToken = null;
  }

  // ==================== REQUEST SECURITY ====================

  /**
   * Make secure API request
   */
  async secureRequest(endpoint, options = {}) {
    try {
      // Check rate limiting
      if (!(await this.checkRateLimit())) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      // Validate session
      const sessionValidation = await SecurityService.validateSession();
      if (!sessionValidation.valid && options.requireAuth !== false) {
        throw new Error('Authentication required');
      }

      // Prepare request
      const secureOptions = await this.prepareSecureRequest(options);
      const url = `${this.baseURL}${endpoint}`;

      // Log API request
      await SecurityService.logSecurityEvent('api_request', {
        endpoint,
        method: options.method || 'GET',
        timestamp: new Date().toISOString()
      });

      // Make request
      const response = await fetch(url, secureOptions);
      
      // Handle response
      return await this.handleSecureResponse(response, endpoint);

    } catch (error) {
      await SecurityService.logSecurityEvent('api_request_failed', {
        endpoint,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Prepare secure request with encryption and signing
   */
  async prepareSecureRequest(options) {
    const headers = {
      'Content-Type': 'application/json',
      'X-App-Version': '1.0.0',
      'X-Platform': 'mobile',
      ...options.headers
    };

    // Add authentication token
    if (options.requireAuth !== false) {
      const authTokens = await SecurityService.getSecurely('auth_tokens');
      if (authTokens && authTokens.accessToken) {
        headers['Authorization'] = `Bearer ${authTokens.accessToken}`;
      }
    }

    // Add CSRF token
    if (this.csrfToken) {
      headers['X-CSRF-Token'] = this.csrfToken;
    }

    // Add device fingerprint
    const deviceFingerprint = await SecurityService.generateDeviceFingerprint();
    if (deviceFingerprint) {
      headers['X-Device-Fingerprint'] = deviceFingerprint.fingerprint;
    }

    // Add request signature
    const requestSignature = await this.signRequest(options);
    if (requestSignature) {
      headers['X-Request-Signature'] = requestSignature;
    }

    // Encrypt sensitive data
    let body = options.body;
    if (body && options.encrypt) {
      body = await SecurityService.encryptData(JSON.stringify(body));
      headers['X-Encrypted'] = 'true';
    } else if (body && typeof body === 'object') {
      body = JSON.stringify(body);
    }

    return {
      method: options.method || 'GET',
      headers,
      body,
      ...options
    };
  }

  /**
   * Handle secure response with decryption
   */
  async handleSecureResponse(response, endpoint) {
    try {
      // Check response status
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Get response data
      let data = await response.json();

      // Decrypt if encrypted
      if (response.headers.get('X-Encrypted') === 'true') {
        const decryptedData = await SecurityService.decryptData(data);
        data = JSON.parse(decryptedData);
      }

      // Update CSRF token if provided
      const newCsrfToken = response.headers.get('X-CSRF-Token');
      if (newCsrfToken) {
        this.csrfToken = newCsrfToken;
      }

      // Log successful response
      await SecurityService.logSecurityEvent('api_response_success', {
        endpoint,
        status: response.status
      });

      return {
        success: true,
        data,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };

    } catch (error) {
      await SecurityService.logSecurityEvent('api_response_error', {
        endpoint,
        error: error.message,
        status: response.status
      });
      throw error;
    }
  }

  /**
   * Sign request for integrity verification
   */
  async signRequest(options) {
    try {
      const requestData = {
        method: options.method || 'GET',
        body: options.body || '',
        timestamp: Date.now()
      };

      const signature = await SecurityService.encryptData(JSON.stringify(requestData));
      return signature;
    } catch (error) {
      console.error('Request signing failed:', error);
      return null;
    }
  }

  // ==================== RATE LIMITING ====================

  /**
   * Check if request is within rate limits
   */
  async checkRateLimit() {
    try {
      const now = Date.now();
      const windowStart = now - this.rateLimitWindow;

      // Clean old requests
      this.requestQueue = this.requestQueue.filter(
        timestamp => timestamp > windowStart
      );

      // Check if under limit
      if (this.requestQueue.length >= this.maxRequestsPerWindow) {
        await SecurityService.logSecurityEvent('rate_limit_exceeded', {
          requestCount: this.requestQueue.length,
          windowStart: new Date(windowStart).toISOString()
        });
        return false;
      }

      // Add current request
      this.requestQueue.push(now);
      return true;

    } catch (error) {
      console.error('Rate limit check failed:', error);
      return true; // Allow request on error
    }
  }

  // ==================== INPUT VALIDATION ====================

  /**
   * Validate and sanitize input data
   */
  validateInput(data, schema) {
    const errors = [];
    const sanitized = {};

    for (const [key, rules] of Object.entries(schema)) {
      const value = data[key];

      // Check required fields
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`${key} is required`);
        continue;
      }

      // Skip validation if field is optional and empty
      if (!rules.required && (value === undefined || value === null || value === '')) {
        continue;
      }

      // Type validation
      if (rules.type && typeof value !== rules.type) {
        errors.push(`${key} must be of type ${rules.type}`);
        continue;
      }

      // String validation
      if (rules.type === 'string') {
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`${key} must be at least ${rules.minLength} characters`);
        }
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`${key} must be no more than ${rules.maxLength} characters`);
        }
        if (rules.pattern && !rules.pattern.test(value)) {
          errors.push(`${key} format is invalid`);
        }
        
        // Sanitize string
        sanitized[key] = this.sanitizeString(value);
      }

      // Number validation
      if (rules.type === 'number') {
        if (rules.min !== undefined && value < rules.min) {
          errors.push(`${key} must be at least ${rules.min}`);
        }
        if (rules.max !== undefined && value > rules.max) {
          errors.push(`${key} must be no more than ${rules.max}`);
        }
        
        sanitized[key] = value;
      }

      // Array validation
      if (rules.type === 'array') {
        if (rules.minItems && value.length < rules.minItems) {
          errors.push(`${key} must have at least ${rules.minItems} items`);
        }
        if (rules.maxItems && value.length > rules.maxItems) {
          errors.push(`${key} must have no more than ${rules.maxItems} items`);
        }
        
        sanitized[key] = value;
      }

      // Custom validation
      if (rules.validate && typeof rules.validate === 'function') {
        const customError = rules.validate(value);
        if (customError) {
          errors.push(customError);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized
    };
  }

  /**
   * Sanitize string input to prevent XSS
   */
  sanitizeString(str) {
    if (typeof str !== 'string') return str;
    
    return str
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  // ==================== API ENDPOINTS ====================

  /**
   * User-related API calls
   */
  async getUserProfile(userId) {
    return this.secureRequest(`/users/${userId}`, {
      method: 'GET'
    });
  }

  async updateUserProfile(userId, profileData) {
    const validation = this.validateInput(profileData, {
      fullName: { type: 'string', required: true, minLength: 2, maxLength: 100 },
      bio: { type: 'string', maxLength: 500 },
      website: { 
        type: 'string', 
        pattern: /^https?:\/\/.+/,
        validate: (value) => {
          if (value && !value.startsWith('http')) {
            return 'Website must start with http:// or https://';
          }
        }
      }
    });

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    return this.secureRequest(`/users/${userId}`, {
      method: 'PUT',
      body: validation.sanitized,
      encrypt: true
    });
  }

  /**
   * Post-related API calls
   */
  async createPost(postData) {
    const validation = this.validateInput(postData, {
      content: { type: 'string', required: true, maxLength: 2000 },
      imageUrl: { type: 'string' },
      tags: { type: 'array', maxItems: 10 }
    });

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    return this.secureRequest('/posts', {
      method: 'POST',
      body: validation.sanitized,
      encrypt: true
    });
  }

  async getPosts(page = 1, limit = 20) {
    return this.secureRequest(`/posts?page=${page}&limit=${limit}`, {
      method: 'GET'
    });
  }

  async likePost(postId) {
    return this.secureRequest(`/posts/${postId}/like`, {
      method: 'POST'
    });
  }

  async commentOnPost(postId, comment) {
    const validation = this.validateInput({ comment }, {
      comment: { type: 'string', required: true, maxLength: 1000 }
    });

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    return this.secureRequest(`/posts/${postId}/comments`, {
      method: 'POST',
      body: validation.sanitized,
      encrypt: true
    });
  }

  /**
   * Verification-related API calls
   */
  async requestVerification(verificationData) {
    return this.secureRequest('/verification/request', {
      method: 'POST',
      body: verificationData,
      encrypt: true
    });
  }

  async getVerificationStatus(userId) {
    return this.secureRequest(`/verification/status/${userId}`, {
      method: 'GET'
    });
  }

  /**
   * Security-related API calls
   */
  async reportSecurityIncident(incidentData) {
    return this.secureRequest('/security/report', {
      method: 'POST',
      body: incidentData,
      encrypt: true
    });
  }

  async getSecurityLogs() {
    return this.secureRequest('/security/logs', {
      method: 'GET'
    });
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Upload file securely
   */
  async uploadFile(file, type = 'image') {
    try {
      // Validate file
      const validation = this.validateFile(file, type);
      if (!validation.isValid) {
        throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      // Add security headers
      const deviceFingerprint = await SecurityService.generateDeviceFingerprint();
      const headers = {
        'X-Device-Fingerprint': deviceFingerprint?.fingerprint
      };

      const response = await fetch(`${this.baseURL}/upload`, {
        method: 'POST',
        headers,
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      await SecurityService.logSecurityEvent('file_uploaded', {
        type,
        size: file.size,
        name: file.name
      });

      return result;

    } catch (error) {
      await SecurityService.logSecurityEvent('file_upload_failed', {
        error: error.message,
        type
      });
      throw error;
    }
  }

  /**
   * Validate uploaded files
   */
  validateFile(file, type) {
    const errors = [];

    // Size limits (in bytes)
    const sizeLimits = {
      image: 10 * 1024 * 1024, // 10MB
      video: 100 * 1024 * 1024, // 100MB
      document: 5 * 1024 * 1024 // 5MB
    };

    // Allowed types
    const allowedTypes = {
      image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      video: ['video/mp4', 'video/mov', 'video/avi'],
      document: ['application/pdf', 'text/plain']
    };

    // Check file size
    if (file.size > sizeLimits[type]) {
      errors.push(`File size exceeds ${sizeLimits[type] / (1024 * 1024)}MB limit`);
    }

    // Check file type
    if (!allowedTypes[type].includes(file.type)) {
      errors.push(`File type ${file.type} not allowed for ${type}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default new SecureApiService();
