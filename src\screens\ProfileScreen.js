import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SIZES } from '../constants/theme';
import { useSecurity } from '../contexts/SecurityContext';
import SecurityDashboard from '../components/SecurityDashboard';

const ProfileScreen = () => {
  const [followers, setFollowers] = useState(1234);
  const [following, setFollowing] = useState(567);
  const [posts, setPosts] = useState(89);
  const [showSecurityDashboard, setShowSecurityDashboard] = useState(false);

  const {
    user,
    verificationStatus,
    requestVerification,
    logout,
    securityLevel,
    securityScore
  } = useSecurity();

  // Mock user data
  const user = {
    username: 'your_username',
    fullName: 'Your Full Name',
    bio: 'Living my best life in Africa 🌍 | Photographer | Traveler | #AfricaUnited',
    avatar: 'https://via.placeholder.com/100',
    website: 'www.yourwebsite.com',
  };

  // Mock posts grid
  const userPosts = [
    { id: 1, image: 'https://via.placeholder.com/120' },
    { id: 2, image: 'https://via.placeholder.com/120' },
    { id: 3, image: 'https://via.placeholder.com/120' },
    { id: 4, image: 'https://via.placeholder.com/120' },
    { id: 5, image: 'https://via.placeholder.com/120' },
    { id: 6, image: 'https://via.placeholder.com/120' },
  ];

  const handleGetVerification = async () => {
    if (verificationStatus.verified) {
      Alert.alert('Already Verified', 'Your account is already verified with a free badge!');
      return;
    }

    Alert.alert(
      'Free Verification Badge',
      'Congratulations! You are eligible for a free verification badge. This badge shows that your account is authentic and helps build trust with your followers.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Get Verified',
          onPress: async () => {
            const result = await requestVerification(user?.id);
            if (result.success) {
              Alert.alert('Success!', result.message || 'Your account has been verified! 🎉');
            } else {
              Alert.alert('Error', result.error || 'Failed to verify account');
            }
          },
        },
      ]
    );
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? Your session will be securely terminated.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            const result = await logout();
            if (!result.success) {
              Alert.alert('Error', 'Failed to logout properly');
            }
          }
        }
      ]
    );
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setShowSecurityDashboard(true)}>
          <Ionicons name="shield-checkmark" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <View style={styles.usernameContainer}>
          <Text style={styles.headerUsername}>{user?.username || 'your_username'}</Text>
          {verificationStatus.verified && (
            <Ionicons
              name="checkmark-circle"
              size={18}
              color={COLORS.verificationGreen}
              style={styles.headerVerificationBadge}
            />
          )}
        </View>
        <TouchableOpacity onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Info */}
        <View style={styles.profileSection}>
          <View style={styles.profileHeader}>
            <Image source={{ uri: user.avatar }} style={styles.profileImage} />
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{formatNumber(posts)}</Text>
                <Text style={styles.statLabel}>Posts</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{formatNumber(followers)}</Text>
                <Text style={styles.statLabel}>Followers</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{formatNumber(following)}</Text>
                <Text style={styles.statLabel}>Following</Text>
              </View>
            </View>
          </View>

          <View style={styles.profileDetails}>
            <View style={styles.nameContainer}>
              <Text style={styles.fullName}>{user?.fullName || 'Your Full Name'}</Text>
              {verificationStatus.verified && (
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={COLORS.verificationGreen}
                  style={styles.verificationBadge}
                />
              )}
            </View>
            <Text style={styles.bio}>{user?.bio || 'Living my best life in Africa 🌍 | Photographer | Traveler | #AfricaUnited'}</Text>
            <Text style={styles.website}>{user?.website || 'www.yourwebsite.com'}</Text>

            {/* Security Level Indicator */}
            <View style={styles.securityIndicator}>
              <Ionicons name="shield" size={16} color={getSecurityColor()} />
              <Text style={[styles.securityText, { color: getSecurityColor() }]}>
                {securityLevel.toUpperCase()} SECURITY ({securityScore}%)
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.editButton}>
              <Text style={styles.editButtonText}>Edit Profile</Text>
            </TouchableOpacity>

            {!verificationStatus.verified && (
              <TouchableOpacity
                style={styles.verificationButton}
                onPress={handleGetVerification}
              >
                <Ionicons name="checkmark-circle-outline" size={16} color={COLORS.white} />
                <Text style={styles.verificationButtonText}>Get Free Verification</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.securityButton}
              onPress={() => setShowSecurityDashboard(true)}
            >
              <Ionicons name="shield-checkmark-outline" size={16} color={COLORS.primary} />
              <Text style={styles.securityButtonText}>Security Dashboard</Text>
            </TouchableOpacity>
          </View>

          {/* Verification Status */}
          {verificationStatus.verified && (
            <View style={styles.verificationStatus}>
              <Ionicons name="checkmark-circle" size={20} color={COLORS.verificationGreen} />
              <Text style={styles.verificationStatusText}>
                ✅ Verified Account - Free verification badge active
              </Text>
              <Text style={styles.verificationDate}>
                Verified on {new Date(verificationStatus.approvedAt).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>

        {/* Posts Grid */}
        <View style={styles.postsSection}>
          <View style={styles.postsHeader}>
            <TouchableOpacity style={[styles.tabButton, styles.activeTab]}>
              <Ionicons name="grid-outline" size={20} color={COLORS.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.tabButton}>
              <Ionicons name="play-outline" size={20} color={COLORS.gray} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.tabButton}>
              <Ionicons name="bookmark-outline" size={20} color={COLORS.gray} />
            </TouchableOpacity>
          </View>

          <View style={styles.postsGrid}>
            {userPosts.map((post, index) => (
              <TouchableOpacity key={post.id} style={styles.postItem}>
                <Image source={{ uri: post.image }} style={styles.postImage} />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Security Dashboard Modal */}
      <SecurityDashboard
        visible={showSecurityDashboard}
        onClose={() => setShowSecurityDashboard(false)}
      />
    </SafeAreaView>
  );

  // Helper function for security color
  function getSecurityColor() {
    switch (securityLevel) {
      case 'maximum': return COLORS.success;
      case 'high': return COLORS.primary;
      case 'medium': return COLORS.warning;
      default: return COLORS.error;
    }
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
    paddingVertical: 12,
    backgroundColor: COLORS.primary,
  },
  usernameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerUsername: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  headerVerificationBadge: {
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  profileSection: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 20,
  },
  statsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  profileDetails: {
    marginBottom: 16,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  fullName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  verificationBadge: {
    marginLeft: 4,
  },
  bio: {
    fontSize: 14,
    color: COLORS.textPrimary,
    lineHeight: 18,
    marginBottom: 4,
  },
  website: {
    fontSize: 14,
    color: COLORS.primary,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  editButton: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  verificationButton: {
    flexDirection: 'row',
    backgroundColor: COLORS.verificationGreen,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    gap: 4,
  },
  verificationButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.white,
  },
  verificationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  verificationStatusText: {
    fontSize: 12,
    color: COLORS.verificationGreen,
    marginLeft: 8,
    fontWeight: '500',
  },
  verificationDate: {
    fontSize: 10,
    color: COLORS.textSecondary,
    marginLeft: 28,
    marginTop: 2,
  },
  securityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  securityText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  securityButton: {
    flexDirection: 'row',
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    gap: 4,
    marginTop: 8,
  },
  securityButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  postsSection: {
    backgroundColor: COLORS.white,
    marginTop: 8,
  },
  postsHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primary,
  },
  postsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  postItem: {
    width: '33.33%',
    aspectRatio: 1,
    padding: 1,
  },
  postImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});

export default ProfileScreen;
