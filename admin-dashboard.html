<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram + Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            padding: 20px;
            overflow-y: auto;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
        }
        
        .nav-item i {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #228B22, #32CD32);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #228B22;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .positive {
            background: #e8f5e8;
            color: #228B22;
        }
        
        .negative {
            background: #ffeaea;
            color: #dc3545;
        }
        
        .dashboard-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #228B22;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .user-table th,
        .user-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .verified-badge {
            background: #00C851;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .security-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .level-high {
            background: #e8f5e8;
            color: #228B22;
        }
        
        .level-medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .level-low {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-btn {
            background: #228B22;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .action-btn:hover {
            background: #1e7b1e;
        }
        
        .security-alerts {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-high {
            background: #ffeaea;
            border-color: #dc3545;
        }
        
        .alert-medium {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .alert-low {
            background: #e8f5e8;
            border-color: #28a745;
        }
        
        .alert-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .alert-time {
            font-size: 12px;
            color: #666;
        }
        
        .chart-container {
            height: 300px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">📱 Instagram + Admin</div>
        
        <div class="nav-item active">
            <span>📊</span>
            <span>Dashboard</span>
        </div>
        
        <div class="nav-item">
            <span>👥</span>
            <span>Users</span>
        </div>
        
        <div class="nav-item">
            <span>✅</span>
            <span>Verifications</span>
        </div>
        
        <div class="nav-item">
            <span>🛡️</span>
            <span>Security</span>
        </div>
        
        <div class="nav-item">
            <span>📱</span>
            <span>App Analytics</span>
        </div>
        
        <div class="nav-item">
            <span>💰</span>
            <span>Revenue</span>
        </div>
        
        <div class="nav-item">
            <span>⚙️</span>
            <span>Settings</span>
        </div>
    </div>
    
    <div class="main-content">
        <div class="header">
            <div>
                <h1>Instagram + Admin Dashboard</h1>
                <p>Real-time monitoring and management</p>
            </div>
            <div>
                <span style="background: #00C851; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                    🟢 ALL SYSTEMS OPERATIONAL
                </span>
            </div>
        </div>
        
        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">2.4M</div>
                <div class="stat-label">Total Users</div>
                <div class="stat-change positive">↗ +12.5% this month</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">1.8M</div>
                <div class="stat-label">Verified Users</div>
                <div class="stat-change positive">↗ +25.3% (FREE badges!)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">99.9%</div>
                <div class="stat-label">Security Score</div>
                <div class="stat-change positive">↗ Maximum Level</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">45.2M</div>
                <div class="stat-label">Posts Today</div>
                <div class="stat-change positive">↗ +8.7% vs yesterday</div>
            </div>
        </div>
        
        <!-- Recent Users -->
        <div class="dashboard-section">
            <div class="section-title">
                <span>👥</span>
                <span>Recent User Registrations</span>
            </div>
            
            <table class="user-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Email</th>
                        <th>Verification</th>
                        <th>Security Level</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #228B22; color: white; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">AM</div>
                                <div>
                                    <div style="font-weight: bold;">Amara Lagos</div>
                                    <div style="font-size: 12px; color: #666;">@amara_lagos</div>
                                </div>
                            </div>
                        </td>
                        <td><EMAIL></td>
                        <td><span class="verified-badge">✓ VERIFIED</span></td>
                        <td><span class="security-level level-high">HIGH</span></td>
                        <td>2 hours ago</td>
                        <td>
                            <button class="action-btn">View</button>
                            <button class="action-btn">Edit</button>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #32CD32; color: white; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">KA</div>
                                <div>
                                    <div style="font-weight: bold;">Kwame Accra</div>
                                    <div style="font-size: 12px; color: #666;">@kwame_accra</div>
                                </div>
                            </div>
                        </td>
                        <td><EMAIL></td>
                        <td><span class="verified-badge">✓ VERIFIED</span></td>
                        <td><span class="security-level level-high">HIGH</span></td>
                        <td>4 hours ago</td>
                        <td>
                            <button class="action-btn">View</button>
                            <button class="action-btn">Edit</button>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: #FFD700; color: white; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">FC</div>
                                <div>
                                    <div style="font-weight: bold;">Fatima Cairo</div>
                                    <div style="font-size: 12px; color: #666;">@fatima_cairo</div>
                                </div>
                            </div>
                        </td>
                        <td><EMAIL></td>
                        <td><span class="verified-badge">✓ VERIFIED</span></td>
                        <td><span class="security-level level-medium">MEDIUM</span></td>
                        <td>6 hours ago</td>
                        <td>
                            <button class="action-btn">View</button>
                            <button class="action-btn">Edit</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Security Alerts -->
        <div class="dashboard-section">
            <div class="section-title">
                <span>🛡️</span>
                <span>Security Alerts</span>
            </div>
            
            <div class="security-alerts">
                <div class="alert-item alert-low">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">
                        <div class="alert-title">System Security Check Passed</div>
                        <div class="alert-time">All security protocols functioning normally - 2 minutes ago</div>
                    </div>
                </div>
                
                <div class="alert-item alert-medium">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <div class="alert-title">New Device Login Detected</div>
                        <div class="alert-time">User @amara_lagos logged in from new device - 15 minutes ago</div>
                    </div>
                </div>
                
                <div class="alert-item alert-low">
                    <div class="alert-icon">🔐</div>
                    <div class="alert-content">
                        <div class="alert-title">Biometric Authentication Success</div>
                        <div class="alert-time">1,247 successful biometric logins in the last hour - 30 minutes ago</div>
                    </div>
                </div>
                
                <div class="alert-item alert-low">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">
                        <div class="alert-title">Free Verification Badges Granted</div>
                        <div class="alert-time">156 new users received free verification badges - 1 hour ago</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Analytics Chart -->
        <div class="dashboard-section">
            <div class="section-title">
                <span>📈</span>
                <span>User Growth Analytics</span>
            </div>
            
            <div class="chart-container">
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 10px;">📊</div>
                    <div>Interactive charts would be displayed here</div>
                    <div style="margin-top: 10px; font-size: 12px;">
                        Showing exponential growth due to FREE verification feature
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simulate real-time updates
        function updateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                if (stat.textContent.includes('M')) {
                    // Simulate user growth
                    const current = parseFloat(stat.textContent);
                    const increment = Math.random() * 0.01;
                    stat.textContent = (current + increment).toFixed(1) + 'M';
                }
            });
        }
        
        // Update stats every 5 seconds
        setInterval(updateStats, 5000);
        
        // Add click handlers for navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Simulate new alerts
        function addNewAlert() {
            const alerts = [
                {
                    type: 'alert-low',
                    icon: '✅',
                    title: 'New User Verified',
                    time: 'User received free verification badge - just now'
                },
                {
                    type: 'alert-low',
                    icon: '🔐',
                    title: 'Security Scan Complete',
                    time: 'All systems secure - just now'
                },
                {
                    type: 'alert-medium',
                    icon: '📱',
                    title: 'High App Usage',
                    time: 'Peak usage detected - just now'
                }
            ];
            
            const randomAlert = alerts[Math.floor(Math.random() * alerts.length)];
            const alertsContainer = document.querySelector('.security-alerts');
            
            const newAlert = document.createElement('div');
            newAlert.className = `alert-item ${randomAlert.type}`;
            newAlert.innerHTML = `
                <div class="alert-icon">${randomAlert.icon}</div>
                <div class="alert-content">
                    <div class="alert-title">${randomAlert.title}</div>
                    <div class="alert-time">${randomAlert.time}</div>
                </div>
            `;
            
            alertsContainer.insertBefore(newAlert, alertsContainer.firstChild);
            
            // Remove old alerts to keep list manageable
            const alerts_list = alertsContainer.querySelectorAll('.alert-item');
            if (alerts_list.length > 6) {
                alertsContainer.removeChild(alerts_list[alerts_list.length - 1]);
            }
        }
        
        // Add new alert every 10 seconds
        setInterval(addNewAlert, 10000);
        
        console.log('Instagram + Admin Dashboard loaded successfully! 🚀');
    </script>
</body>
</html>
