{"version": 3, "names": ["React", "StyleSheet", "StackScreenNativeComponent", "StackScreenLifecycleState", "INITIAL", "DETACHED", "ATTACHED", "StackScreen", "children", "maxLifecycleState", "<PERSON><PERSON><PERSON>", "onWillAppear", "onWillDisappear", "onDidAppear", "onDidDisappear", "onPop", "handleOnDidDisappear", "useCallback", "e", "createElement", "style", "absoluteFill"], "sourceRoot": "../../../../src", "sources": ["components/gamma/StackScreen.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,0BAA0B,MAAM,+CAA+C;AAItF,OAAO,MAAMC,yBAAyB,GAAG;EACvCC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE;AACZ,CAAU;AAaV;AACA;AACA;AACA,SAASC,WAAWA,CAAC;EACnBC,QAAQ;EACR;EACAC,iBAAiB;EACjBC,SAAS;EACT;EACAC,YAAY;EACZC,eAAe;EACfC,WAAW;EACXC,cAAc;EACd;EACAC;AACgB,CAAC,EAAE;EACnB,MAAMC,oBAAoB,GAAGhB,KAAK,CAACiB,WAAW,CAC3CC,CAA8C,IAAK;IAClDJ,cAAc,GAAGI,CAAC,CAAC;IACnBH,KAAK,GAAGL,SAAS,CAAC;EACpB,CAAC,EACD,CAACI,cAAc,EAAEC,KAAK,EAAEL,SAAS,CACnC,CAAC;EAED,oBACEV,KAAA,CAAAmB,aAAA,CAACjB,0BAA0B;IACzBkB,KAAK,EAAEnB,UAAU,CAACoB;IAClB;IAAA;IACAZ,iBAAiB,EAAEA,iBAAkB;IACrCC,SAAS,EAAEA;IACX;IAAA;IACAC,YAAY,EAAEA,YAAa;IAC3BE,WAAW,EAAEA,WAAY;IACzBD,eAAe,EAAEA,eAAgB;IACjCE,cAAc,EAAEE;EAAqB,GACpCR,QACyB,CAAC;AAEjC;AAEA,eAAeD,WAAW", "ignoreList": []}