import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import * as Crypto from 'expo-crypto';
import * as Device from 'expo-device';
import * as Application from 'expo-application';
import Constants from 'expo-constants';

/**
 * Advanced Security Service for Africa Social
 * Implements multiple layers of security including:
 * - Biometric authentication
 * - Secure token storage
 * - Device fingerprinting
 * - Data encryption
 * - Session management
 * - Fraud detection
 */
class SecurityService {
  constructor() {
    this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
    this.maxLoginAttempts = 5;
    this.lockoutDuration = 15 * 60 * 1000; // 15 minutes
  }

  // ==================== BIOMETRIC AUTHENTICATION ====================

  /**
   * Check if biometric authentication is available
   */
  async isBiometricAvailable() {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
        hasHardware,
        isEnrolled
      };
    } catch (error) {
      console.error('Biometric check failed:', error);
      return { available: false, types: [], hasHardware: false, isEnrolled: false };
    }
  }

  /**
   * Authenticate user with biometrics
   */
  async authenticateWithBiometrics(promptMessage = 'Authenticate to access Instagram +') {
    try {
      const biometricStatus = await this.isBiometricAvailable();

      if (!biometricStatus.available) {
        throw new Error('Biometric authentication not available');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      if (result.success) {
        await this.logSecurityEvent('biometric_auth_success');
        return { success: true, method: 'biometric' };
      } else {
        await this.logSecurityEvent('biometric_auth_failed', { error: result.error });
        return { success: false, error: result.error };
      }
    } catch (error) {
      await this.logSecurityEvent('biometric_auth_error', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  // ==================== SECURE STORAGE ====================

  /**
   * Store sensitive data securely
   */
  async storeSecurely(key, value, options = {}) {
    try {
      const encryptedValue = await this.encryptData(JSON.stringify(value));
      await SecureStore.setItemAsync(key, encryptedValue, {
        requireAuthentication: options.requireAuth || false,
        authenticationPrompt: options.authPrompt || 'Authenticate to access secure data',
        ...options
      });
      return true;
    } catch (error) {
      console.error('Secure storage failed:', error);
      await this.logSecurityEvent('secure_store_failed', { key, error: error.message });
      return false;
    }
  }

  /**
   * Retrieve sensitive data securely
   */
  async getSecurely(key, options = {}) {
    try {
      const encryptedValue = await SecureStore.getItemAsync(key, options);
      if (!encryptedValue) return null;

      const decryptedValue = await this.decryptData(encryptedValue);
      return JSON.parse(decryptedValue);
    } catch (error) {
      console.error('Secure retrieval failed:', error);
      await this.logSecurityEvent('secure_retrieve_failed', { key, error: error.message });
      return null;
    }
  }

  /**
   * Delete secure data
   */
  async deleteSecurely(key) {
    try {
      await SecureStore.deleteItemAsync(key);
      return true;
    } catch (error) {
      console.error('Secure deletion failed:', error);
      return false;
    }
  }

  // ==================== DATA ENCRYPTION ====================

  /**
   * Encrypt sensitive data
   */
  async encryptData(data) {
    try {
      const key = await this.getOrCreateEncryptionKey();
      // In a real implementation, you'd use a proper encryption library
      // For demo purposes, we'll use base64 encoding with a salt
      const salt = await Crypto.getRandomBytesAsync(16);
      const saltedData = salt + data;
      return Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, saltedData);
    } catch (error) {
      console.error('Encryption failed:', error);
      throw error;
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptData(encryptedData) {
    try {
      // In a real implementation, you'd properly decrypt the data
      // For demo purposes, we'll return the data as-is
      return encryptedData;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw error;
    }
  }

  /**
   * Get or create encryption key
   */
  async getOrCreateEncryptionKey() {
    try {
      let key = await SecureStore.getItemAsync('encryption_key');
      if (!key) {
        key = await Crypto.getRandomBytesAsync(32);
        await SecureStore.setItemAsync('encryption_key', key.toString());
      }
      return key;
    } catch (error) {
      console.error('Key generation failed:', error);
      throw error;
    }
  }

  // ==================== DEVICE FINGERPRINTING ====================

  /**
   * Generate device fingerprint for fraud detection
   */
  async generateDeviceFingerprint() {
    try {
      const deviceInfo = {
        deviceId: await Application.getAndroidId() || Constants.deviceId,
        deviceName: Device.deviceName,
        deviceType: Device.deviceType,
        brand: Device.brand,
        manufacturer: Device.manufacturer,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
        platformApiLevel: Device.platformApiLevel,
        totalMemory: Device.totalMemory,
        supportedCpuArchitectures: Device.supportedCpuArchitectures,
        isDevice: Device.isDevice,
        isRooted: await this.checkIfDeviceIsRooted(),
        appVersion: Application.nativeApplicationVersion,
        buildVersion: Application.nativeBuildVersion,
        installationId: Constants.installationId,
        sessionId: Constants.sessionId,
      };

      // Create a hash of the device info
      const fingerprint = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        JSON.stringify(deviceInfo)
      );

      return {
        fingerprint,
        deviceInfo,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Device fingerprinting failed:', error);
      return null;
    }
  }

  /**
   * Check if device is rooted/jailbroken
   */
  async checkIfDeviceIsRooted() {
    try {
      // This is a simplified check - in production, use a dedicated library
      return Device.isDevice === false; // Emulator detection
    } catch (error) {
      return false;
    }
  }

  // ==================== SESSION MANAGEMENT ====================

  /**
   * Create secure session
   */
  async createSession(userId, deviceFingerprint) {
    try {
      const sessionId = await Crypto.getRandomBytesAsync(32);
      const sessionData = {
        sessionId: sessionId.toString(),
        userId,
        deviceFingerprint,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true
      };

      await this.storeSecurely('current_session', sessionData);
      await this.logSecurityEvent('session_created', { userId, sessionId: sessionData.sessionId });

      return sessionData;
    } catch (error) {
      console.error('Session creation failed:', error);
      return null;
    }
  }

  /**
   * Validate session
   */
  async validateSession() {
    try {
      const session = await this.getSecurely('current_session');
      if (!session) return { valid: false, reason: 'No session found' };

      const now = new Date();
      const lastActivity = new Date(session.lastActivity);
      const timeDiff = now - lastActivity;

      if (timeDiff > this.sessionTimeout) {
        await this.invalidateSession();
        return { valid: false, reason: 'Session expired' };
      }

      // Update last activity
      session.lastActivity = now.toISOString();
      await this.storeSecurely('current_session', session);

      return { valid: true, session };
    } catch (error) {
      console.error('Session validation failed:', error);
      return { valid: false, reason: 'Validation error' };
    }
  }

  /**
   * Invalidate session
   */
  async invalidateSession() {
    try {
      await this.deleteSecurely('current_session');
      await this.logSecurityEvent('session_invalidated');
      return true;
    } catch (error) {
      console.error('Session invalidation failed:', error);
      return false;
    }
  }

  // ==================== FRAUD DETECTION ====================

  /**
   * Detect suspicious login attempts
   */
  async detectSuspiciousActivity(userId, deviceFingerprint) {
    try {
      const loginAttempts = await this.getSecurely(`login_attempts_${userId}`) || [];
      const now = new Date();

      // Clean old attempts (older than lockout duration)
      const recentAttempts = loginAttempts.filter(
        attempt => (now - new Date(attempt.timestamp)) < this.lockoutDuration
      );

      // Check for too many failed attempts
      const failedAttempts = recentAttempts.filter(attempt => !attempt.success);
      if (failedAttempts.length >= this.maxLoginAttempts) {
        await this.logSecurityEvent('account_locked', { userId, attempts: failedAttempts.length });
        return {
          suspicious: true,
          reason: 'Too many failed login attempts',
          lockoutUntil: new Date(now.getTime() + this.lockoutDuration).toISOString()
        };
      }

      // Check for device fingerprint mismatch
      const lastKnownDevice = await this.getSecurely(`device_${userId}`);
      if (lastKnownDevice && lastKnownDevice.fingerprint !== deviceFingerprint.fingerprint) {
        await this.logSecurityEvent('new_device_detected', { userId, newDevice: deviceFingerprint });
        return {
          suspicious: true,
          reason: 'New device detected',
          requiresVerification: true
        };
      }

      return { suspicious: false };
    } catch (error) {
      console.error('Fraud detection failed:', error);
      return { suspicious: false };
    }
  }

  /**
   * Log login attempt
   */
  async logLoginAttempt(userId, success, deviceFingerprint, additionalInfo = {}) {
    try {
      const loginAttempts = await this.getSecurely(`login_attempts_${userId}`) || [];

      const attempt = {
        timestamp: new Date().toISOString(),
        success,
        deviceFingerprint: deviceFingerprint.fingerprint,
        ...additionalInfo
      };

      loginAttempts.push(attempt);

      // Keep only last 20 attempts
      const recentAttempts = loginAttempts.slice(-20);
      await this.storeSecurely(`login_attempts_${userId}`, recentAttempts);

      if (success) {
        // Store device fingerprint for future reference
        await this.storeSecurely(`device_${userId}`, deviceFingerprint);
      }

      return true;
    } catch (error) {
      console.error('Login attempt logging failed:', error);
      return false;
    }
  }

  // ==================== SECURITY LOGGING ====================

  /**
   * Log security events
   */
  async logSecurityEvent(eventType, data = {}) {
    try {
      const securityLogs = await this.getSecurely('security_logs') || [];

      const logEntry = {
        timestamp: new Date().toISOString(),
        eventType,
        data,
        deviceInfo: await this.generateDeviceFingerprint()
      };

      securityLogs.push(logEntry);

      // Keep only last 100 security events
      const recentLogs = securityLogs.slice(-100);
      await this.storeSecurely('security_logs', recentLogs);

      // In production, also send to server for monitoring
      console.log('Security Event:', logEntry);

      return true;
    } catch (error) {
      console.error('Security logging failed:', error);
      return false;
    }
  }

  /**
   * Get security logs
   */
  async getSecurityLogs() {
    try {
      return await this.getSecurely('security_logs') || [];
    } catch (error) {
      console.error('Failed to retrieve security logs:', error);
      return [];
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Clear all security data (for logout)
   */
  async clearSecurityData() {
    try {
      await this.deleteSecurely('current_session');
      await this.deleteSecurely('encryption_key');
      await this.logSecurityEvent('security_data_cleared');
      return true;
    } catch (error) {
      console.error('Failed to clear security data:', error);
      return false;
    }
  }

  /**
   * Generate secure random token
   */
  async generateSecureToken(length = 32) {
    try {
      const randomBytes = await Crypto.getRandomBytesAsync(length);
      return randomBytes.toString();
    } catch (error) {
      console.error('Token generation failed:', error);
      return null;
    }
  }
}

export default new SecurityService();
