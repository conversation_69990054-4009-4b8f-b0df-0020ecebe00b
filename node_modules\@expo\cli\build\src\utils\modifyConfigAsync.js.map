{"version": 3, "sources": ["../../../src/utils/modifyConfigAsync.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SilentError } from './errors';\nimport * as Log from '../log';\n\n/** Wraps `[@expo/config] modifyConfigAsync()` and adds additional logging. */\nexport async function attemptModification(\n  projectRoot: string,\n  edits: Partial<ExpoConfig>,\n  exactEdits: Partial<ExpoConfig>\n): Promise<boolean> {\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n  });\n  if (modification.type !== 'success') {\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n  return modification.type === 'success';\n}\n\nexport function warnAboutConfigAndThrow(type: string, message: string, edits: Partial<ExpoConfig>) {\n  Log.log();\n  if (type === 'warn') {\n    // The project is using a dynamic config, give the user a helpful log and bail out.\n    Log.log(chalk.yellow(message));\n  }\n  notifyAboutManualConfigEdits(edits);\n  throw new SilentError();\n}\n\nfunction notifyAboutManualConfigEdits(edits: Partial<ExpoConfig>) {\n  Log.log(chalk.cyan(`Add the following to your Expo config`));\n  Log.log();\n  Log.log(JSON.stringify(edits, null, 2));\n  Log.log();\n}\n"], "names": ["attemptModification", "warnAboutConfigAndThrow", "projectRoot", "edits", "exactEdits", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "type", "message", "Log", "log", "chalk", "yellow", "notifyAboutManualConfigEdits", "SilentError", "cyan", "JSON", "stringify"], "mappings": ";;;;;;;;;;;IAOsBA,mBAAmB;eAAnBA;;IAcNC,uBAAuB;eAAvBA;;;;yBArB8B;;;;;;;gEAC5B;;;;;;wBAEU;6DACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGd,eAAeD,oBACpBE,WAAmB,EACnBC,KAA0B,EAC1BC,UAA+B;IAE/B,MAAMC,eAAe,MAAMC,IAAAA,2BAAiB,EAACJ,aAAaC,OAAO;QAC/DI,2BAA2B;IAC7B;IACA,IAAIF,aAAaG,IAAI,KAAK,WAAW;QACnCP,wBAAwBI,aAAaG,IAAI,EAAEH,aAAaI,OAAO,EAAGL;IACpE;IACA,OAAOC,aAAaG,IAAI,KAAK;AAC/B;AAEO,SAASP,wBAAwBO,IAAY,EAAEC,OAAe,EAAEN,KAA0B;IAC/FO,KAAIC,GAAG;IACP,IAAIH,SAAS,QAAQ;QACnB,mFAAmF;QACnFE,KAAIC,GAAG,CAACC,gBAAK,CAACC,MAAM,CAACJ;IACvB;IACAK,6BAA6BX;IAC7B,MAAM,IAAIY,mBAAW;AACvB;AAEA,SAASD,6BAA6BX,KAA0B;IAC9DO,KAAIC,GAAG,CAACC,gBAAK,CAACI,IAAI,CAAC,CAAC,qCAAqC,CAAC;IAC1DN,KAAIC,GAAG;IACPD,KAAIC,GAAG,CAACM,KAAKC,SAAS,CAACf,OAAO,MAAM;IACpCO,KAAIC,GAAG;AACT"}