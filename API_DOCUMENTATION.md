# 📡 Instagram + API Documentation

## Overview

Instagram + provides a comprehensive REST API with GraphQL support, real-time subscriptions, and advanced security features. All endpoints are secured with enterprise-grade authentication and encryption.

## 🔐 Authentication

### Base URL
```
Production: https://api.instagramplus.com/v1
Staging: https://staging-api.instagramplus.com/v1
```

### Authentication Methods

#### 1. JWT Bearer Token
```http
Authorization: Bearer <jwt_token>
```

#### 2. API Key (for server-to-server)
```http
X-API-Key: <api_key>
X-API-Secret: <api_secret>
```

#### 3. Biometric Authentication
```http
X-Biometric-Token: <biometric_hash>
X-Device-Fingerprint: <device_fingerprint>
```

## 📱 Core Endpoints

### Authentication Endpoints

#### POST /auth/register
Register a new user with free verification eligibility.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "fullName": "<PERSON>",
  "username": "johndo<PERSON>",
  "deviceFingerprint": "device_hash_here"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "username": "johndoe",
    "fullName": "John Doe",
    "verified": false,
    "verificationEligible": true,
    "securityLevel": "medium",
    "createdAt": "2025-01-19T10:00:00Z"
  },
  "tokens": {
    "accessToken": "jwt_access_token",
    "refreshToken": "jwt_refresh_token",
    "expiresIn": 3600
  }
}
```

#### POST /auth/login
Secure login with multiple authentication factors.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "deviceFingerprint": "device_hash_here",
  "biometricData": "optional_biometric_hash"
}
```

#### POST /auth/biometric-login
Login using biometric authentication.

**Request:**
```json
{
  "biometricHash": "biometric_signature",
  "deviceFingerprint": "device_hash_here",
  "userId": "user_123"
}
```

### User Management

#### GET /users/me
Get current user profile with security status.

**Response:**
```json
{
  "id": "user_123",
  "username": "johndoe",
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "bio": "Living my best life! 🌟",
  "avatar": "https://cdn.instagramplus.com/avatars/user_123.jpg",
  "verified": true,
  "verificationBadge": {
    "type": "free_verification",
    "grantedAt": "2025-01-19T10:30:00Z",
    "reason": "Authentic user verification"
  },
  "securityProfile": {
    "level": "high",
    "score": 85,
    "biometricEnabled": true,
    "deviceSecure": true,
    "lastSecurityCheck": "2025-01-19T10:00:00Z"
  },
  "stats": {
    "followers": 1234,
    "following": 567,
    "posts": 89
  }
}
```

#### PUT /users/me
Update user profile.

#### POST /users/me/verify
Request free verification badge.

**Request:**
```json
{
  "verificationType": "free_verification",
  "additionalInfo": {
    "reason": "Authentic user account",
    "socialProof": ["twitter.com/johndoe"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "verification": {
    "status": "approved",
    "badgeType": "free_verification",
    "grantedAt": "2025-01-19T10:30:00Z",
    "message": "Congratulations! Your free verification badge has been granted."
  }
}
```

### Posts & Content

#### GET /posts/feed
Get personalized feed with advanced algorithms.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Posts per page (default: 20, max: 50)
- `algorithm`: feed_algorithm (default: "smart")

**Response:**
```json
{
  "posts": [
    {
      "id": "post_456",
      "author": {
        "id": "user_789",
        "username": "amara_lagos",
        "fullName": "Amara Lagos",
        "avatar": "https://cdn.instagramplus.com/avatars/user_789.jpg",
        "verified": true,
        "verificationBadge": {
          "type": "free_verification"
        }
      },
      "content": {
        "caption": "Beautiful sunset in Lagos! 🌅 #Lagos #Sunset",
        "images": [
          {
            "url": "https://cdn.instagramplus.com/posts/post_456_1.jpg",
            "width": 1080,
            "height": 1080
          }
        ],
        "hashtags": ["Lagos", "Sunset"],
        "mentions": []
      },
      "engagement": {
        "likes": 234,
        "comments": 45,
        "shares": 12,
        "views": 1567
      },
      "createdAt": "2025-01-19T08:00:00Z",
      "location": {
        "name": "Lagos, Nigeria",
        "coordinates": [3.3792, 6.5244]
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1000,
    "hasNext": true
  }
}
```

#### POST /posts
Create a new post.

**Request (multipart/form-data):**
```
caption: "My new post! #InstagramPlus"
images: [file1.jpg, file2.jpg]
location: "Lagos, Nigeria"
hashtags: ["InstagramPlus", "Social"]
```

#### POST /posts/:id/like
Like/unlike a post.

#### POST /posts/:id/comments
Add comment to a post.

### Security Endpoints

#### GET /security/status
Get current security status and recommendations.

**Response:**
```json
{
  "securityLevel": "high",
  "score": 85,
  "factors": {
    "deviceSecurity": {
      "status": "secure",
      "score": 90,
      "details": "Device not rooted/jailbroken"
    },
    "biometricAuth": {
      "status": "enabled",
      "score": 95,
      "types": ["fingerprint", "face"]
    },
    "sessionSecurity": {
      "status": "active",
      "score": 80,
      "lastActivity": "2025-01-19T10:00:00Z"
    },
    "accountVerification": {
      "status": "verified",
      "score": 100,
      "badgeType": "free_verification"
    }
  },
  "recommendations": [
    "Enable two-factor authentication for maximum security"
  ],
  "threats": {
    "detected": 0,
    "blocked": 5,
    "lastThreat": null
  }
}
```

#### POST /security/report-incident
Report security incident.

#### GET /security/logs
Get security event logs.

### Real-time Features

#### WebSocket Connection
```
wss://ws.instagramplus.com/v1/realtime
```

#### Subscription Events
- `post.new` - New posts in feed
- `notification.new` - New notifications
- `message.new` - New direct messages
- `security.alert` - Security alerts
- `verification.granted` - Verification status updates

## 🔒 Security Features

### Request Signing
All API requests are signed using HMAC-SHA256:

```javascript
const signature = crypto
  .createHmac('sha256', apiSecret)
  .update(requestBody + timestamp)
  .digest('hex');

headers['X-Signature'] = signature;
headers['X-Timestamp'] = timestamp;
```

### Rate Limiting
- **Standard users**: 1000 requests/hour
- **Verified users**: 2000 requests/hour
- **Premium API**: 10000 requests/hour

### Data Encryption
- All sensitive data encrypted with AES-256
- End-to-end encryption for direct messages
- Secure file upload with virus scanning

## 📊 Analytics Endpoints

#### GET /analytics/user-stats
Get user engagement analytics.

#### GET /analytics/post-performance
Get post performance metrics.

#### GET /analytics/security-insights
Get security analytics and insights.

## 🌍 Localization

### Supported Languages
- English (en)
- French (fr)
- Arabic (ar)
- Swahili (sw)
- Portuguese (pt)
- Spanish (es)

### Headers
```http
Accept-Language: en-US,en;q=0.9
X-Timezone: Africa/Lagos
```

## 🚀 GraphQL API

### Endpoint
```
POST /graphql
```

### Example Query
```graphql
query GetUserFeed($limit: Int = 20) {
  feed(limit: $limit) {
    posts {
      id
      caption
      author {
        username
        verified
        verificationBadge {
          type
        }
      }
      engagement {
        likes
        comments
      }
      createdAt
    }
  }
}
```

## 📱 Mobile SDK

### React Native SDK
```bash
npm install @instagramplus/react-native-sdk
```

### Usage
```javascript
import InstagramPlus from '@instagramplus/react-native-sdk';

const client = new InstagramPlus({
  apiKey: 'your_api_key',
  environment: 'production'
});

// Login with biometric
const result = await client.auth.biometricLogin();

// Get feed
const feed = await client.posts.getFeed();

// Request free verification
const verification = await client.users.requestVerification();
```

## 🔧 Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password",
    "details": {
      "field": "password",
      "reason": "Password does not meet security requirements"
    },
    "timestamp": "2025-01-19T10:00:00Z",
    "requestId": "req_123456"
  }
}
```

### Common Error Codes
- `INVALID_CREDENTIALS` - Authentication failed
- `INSUFFICIENT_PERMISSIONS` - Access denied
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `DEVICE_NOT_SECURE` - Device security check failed
- `VERIFICATION_REQUIRED` - Account verification needed
- `BIOMETRIC_FAILED` - Biometric authentication failed

## 🧪 Testing

### Test Environment
```
Base URL: https://test-api.instagramplus.com/v1
```

### Test Credentials
```
Email: <EMAIL>
Password: TestPass123!
API Key: test_api_key_123
```

## 📞 Support

- **Documentation**: https://docs.instagramplus.com
- **API Status**: https://status.instagramplus.com
- **Developer Support**: <EMAIL>
- **Security Issues**: <EMAIL>

---

**Instagram + API - Empowering developers to build secure, verified social experiences! 🚀**
