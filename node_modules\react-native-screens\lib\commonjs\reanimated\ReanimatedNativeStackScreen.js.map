{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_Screen", "_reactNativeReanimated", "_interopRequireWildcard", "_ReanimatedTransitionProgressContext", "_reactNativeSafeAreaContext", "_getDefaultHeaderHeight", "_getStatusBarHeight", "_ReanimatedHeaderHeightContext", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "AnimatedScreen", "Animated", "createAnimatedComponent", "InnerScreen", "ENABLE_FABRIC", "global", "RN$Bridgeless", "ReanimatedNativeStackScreen", "React", "forwardRef", "props", "ref", "children", "rest", "stackPresentation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimensions", "useSafeAreaFrame", "topInset", "useSafeAreaInsets", "top", "isStatusBarTranslucent", "statusBarTranslucent", "statusBarHeight", "getStatusBarHeight", "defaultHeaderHeight", "getDefaultHeaderHeight", "cachedHeaderHeight", "useRef", "headerHeight", "useSharedValue", "progress", "closing", "goingForward", "createElement", "onTransitionProgressReanimated", "useEvent", "event", "value", "Platform", "OS", "onHeaderHeightChangeReanimated", "current", "Provider", "displayName", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedNativeStackScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAQA,IAAAG,sBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,oCAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,2BAAA,GAAAN,OAAA;AAIA,IAAAO,uBAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,mBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,8BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAA4E,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAApB,uBAAAY,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAAA,SAAAmB,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA,KAT5E;AAWA,MAAMG,cAAc,GAAGC,8BAAQ,CAACC,uBAAuB,CACrDC,mBACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,aAAa,GAAG,CAAC,CAACC,MAAM,EAAEC,aAAa;AAE7C,MAAMC,2BAA2B,gBAAGC,cAAK,CAACC,UAAU,CAGlD,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EACnC,MAAM;IAAEI,iBAAiB,GAAG,MAAM;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAE3D,MAAMG,UAAU,GAAG,IAAAC,4CAAgB,EAAC,CAAC;EACrC,MAAMC,QAAQ,GAAG,IAAAC,6CAAiB,EAAC,CAAC,CAACC,GAAG;EACxC,MAAMC,sBAAsB,GAAGR,IAAI,CAACS,oBAAoB,IAAI,KAAK;EACjE,MAAMC,eAAe,GAAG,IAAAC,2BAAkB,EACxCN,QAAQ,EACRF,UAAU,EACVK,sBACF,CAAC;;EAED;EACA;EACA,MAAMI,mBAAmB,GAAG,IAAAC,+BAAsB,EAChDV,UAAU,EACVO,eAAe,EACfT,iBAAiB,EACjBC,cACF,CAAC;EAED,MAAMY,kBAAkB,GAAGnB,cAAK,CAACoB,MAAM,CAACH,mBAAmB,CAAC;EAC5D,MAAMI,YAAY,GAAG,IAAAC,qCAAc,EAACL,mBAAmB,CAAC;EAExD,MAAMM,QAAQ,GAAG,IAAAD,qCAAc,EAAC,CAAC,CAAC;EAClC,MAAME,OAAO,GAAG,IAAAF,qCAAc,EAAC,CAAC,CAAC;EACjC,MAAMG,YAAY,GAAG,IAAAH,qCAAc,EAAC,CAAC,CAAC;EAEtC,oBACEpE,MAAA,CAAAkB,OAAA,CAAAsD,aAAA,CAAClC;EACC;EAAA,EAAAN,QAAA;IACAiB,GAAG,EAAEA,GAAI;IACTwB,8BAA8B,EAAE,IAAAC,+BAAQ,EACrCC,KAAkC,IAAK;MACtC,SAAS;;MACTN,QAAQ,CAACO,KAAK,GAAGD,KAAK,CAACN,QAAQ;MAC/BC,OAAO,CAACM,KAAK,GAAGD,KAAK,CAACL,OAAO;MAC7BC,YAAY,CAACK,KAAK,GAAGD,KAAK,CAACJ,YAAY;IACzC,CAAC,EACD;IACE;IACA;IACAM,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,sBAAsB;IACtB;IACFpC,aAAa,GACX,sBAAsB,GACtB,uBAAuB,CAE/B,CAAE;IACFqC,8BAA8B,EAAE,IAAAL,+BAAQ,EACrCC,KAAkC,IAAK;MACtC,SAAS;;MACT,IAAIA,KAAK,CAACR,YAAY,KAAKF,kBAAkB,CAACe,OAAO,EAAE;QACrDb,YAAY,CAACS,KAAK,GAAGD,KAAK,CAACR,YAAY;QACvCF,kBAAkB,CAACe,OAAO,GAAGL,KAAK,CAACR,YAAY;MACjD;IACF,CAAC,EACD;IACE;IACAU,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,sBAAsB,GACtBpC,aAAa,GACb,sBAAsB,GACtB,uBAAuB,CAE/B;EAAE,GACES,IAAI,gBACRnD,MAAA,CAAAkB,OAAA,CAAAsD,aAAA,CAAC7D,8BAAA,CAAAO,OAA6B,CAAC+D,QAAQ;IAACL,KAAK,EAAET;EAAa,gBAC1DnE,MAAA,CAAAkB,OAAA,CAAAsD,aAAA,CAACjE,oCAAA,CAAAW,OAAmC,CAAC+D,QAAQ;IAC3CL,KAAK,EAAE;MACLP,QAAQ;MACRC,OAAO;MACPC;IACF;EAAE,GACDrB,QAC2C,CACR,CAC1B,CAAC;AAErB,CAAC,CAAC;AAEFL,2BAA2B,CAACqC,WAAW,GAAG,6BAA6B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAlE,OAAA,GAEzD2B,2BAA2B", "ignoreList": []}