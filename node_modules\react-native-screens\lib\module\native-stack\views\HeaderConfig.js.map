{"version": 3, "names": ["useTheme", "React", "Platform", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderConfig", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "useBackPressSubscription", "processFonts", "warnOnce", "HeaderConfig", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "backButtonDisplayMode", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "onBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "useEffect", "processedSearchBarOptions", "useMemo", "OS", "onFocus", "args", "onClose", "isVisionOS", "isVision", "color", "undefined", "createElement", "backgroundColor", "card", "backTitle", "backTitleFontSize", "fontSize", "backTitleVisible", "blurEffect", "hidden", "hideBackButton", "hideShadow", "largeTitle", "largeTitleBackgroundColor", "largeTitleColor", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "largeTitleHideShadow", "name", "titleColor", "text", "titleFontSize", "titleFontWeight", "topInsetEnabled", "translucent", "onAttached", "onDetached", "key", "source"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/HeaderConfig.tsx"], "mappings": "AAAA,SAAgBA,QAAQ,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,cAAc;AAEvC,SACEC,sCAAsC,EACtCC,sBAAsB,QACjB,aAAa;AACpB,SACEC,gCAAgC,EAChCC,2BAA2B,EAC3BC,uBAAuB,EACvBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,8BAA8B,QACzB,0CAA0C;AACjD,OAAOC,SAAS,MAAM,4BAA4B;AAElD,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,QAAQ,MAAM,WAAW;AAMhC,eAAe,SAASC,YAAYA,CAAC;EACnCC,eAAe;EACfC,sBAAsB;EACtBC,SAAS;EACTC,qBAAqB;EACrBC,qBAAqB,GAAG,SAAS;EACjCC,eAAe;EACfC,oBAAoB,GAAG,CAAC,CAAC;EACzBC,sBAAsB,GAAG,IAAI;EAC7BC,YAAY;EACZC,oBAAoB;EACpBC,gBAAgB;EAChBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,gBAAgB;EAChBC,0BAA0B;EAC1BC,qBAAqB,GAAG,CAAC,CAAC;EAC1BC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,WAAW,GAAG,CAAC,CAAC;EAChBC,eAAe;EACfC,WAAW;EACXC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,qBAAqB,GAAG,IAAI;EAC5BC,iBAAiB;EACjBC,KAAK;EACLC,SAAS;EACTC;AACK,CAAC,EAAe;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG3C,QAAQ,CAAC,CAAC;EAC7B,MAAM4C,SAAS,GAAGT,eAAe,IAAIQ,MAAM,CAACE,OAAO;;EAEnD;EACA;EACA;EACA,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAGrC,wBAAwB,CAAC;IAC3BsC,WAAW,EAAE9C,sBAAsB;IACnC+C,UAAU,EAAE,CAACV,SAAS,IAAI,CAAC,CAACA,SAAS,CAACW;EACxC,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE1C,YAAY,CAAC,CACXS,oBAAoB,CAACkC,UAAU,EAC/B1B,qBAAqB,CAAC0B,UAAU,EAChCnB,gBAAgB,CAACmB,UAAU,CAC5B,CAAC;;EAEJ;EACA;EACAvD,KAAK,CAACwD,SAAS,CAAC,MAAMT,iBAAiB,EAAE,CAACP,SAAS,CAAC,CAAC;EAErD,MAAMiB,yBAAyB,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IACpD,IACEzD,QAAQ,CAAC0D,EAAE,KAAK,SAAS,IACzBnB,SAAS,IACT,CAACA,SAAS,CAACW,yBAAyB,EACpC;MACA,MAAMS,OAAkC,GAAGA,CAAC,GAAGC,IAAI,KAAK;QACtDb,kBAAkB,CAAC,CAAC;QACpBR,SAAS,CAACoB,OAAO,GAAG,GAAGC,IAAI,CAAC;MAC9B,CAAC;MACD,MAAMC,OAAkC,GAAGA,CAAC,GAAGD,IAAI,KAAK;QACtDd,iBAAiB,CAAC,CAAC;QACnBP,SAAS,CAACsB,OAAO,GAAG,GAAGD,IAAI,CAAC;MAC9B,CAAC;MAED,OAAO;QAAE,GAAGrB,SAAS;QAAEoB,OAAO;QAAEE;MAAQ,CAAC;IAC3C;IACA,OAAOtB,SAAS;EAClB,CAAC,EAAE,CAACA,SAAS,EAAEQ,kBAAkB,EAAED,iBAAiB,CAAC,CAAC;;EAEtD;EACA,MAAMgB,UAAU,GAAG9D,QAAQ,EAAE+D,QAAQ;EAErCnD,QAAQ,CACNkD,UAAU,KACP3B,gBAAgB,CAAC6B,KAAK,KAAKC,SAAS,IAAIhC,eAAe,KAAKgC,SAAS,CAAC,EACzE,2EACF,CAAC;EAED,oBACElE,KAAA,CAAAmE,aAAA,CAAC7D,uBAAuB;IACtBU,sBAAsB,EAAEA,sBAAuB;IAC/CoD,eAAe,EACbnC,WAAW,CAACmC,eAAe,GAAGnC,WAAW,CAACmC,eAAe,GAAG1B,MAAM,CAAC2B,IACpE;IACDC,SAAS,EAAElD,eAAgB;IAC3BgC,mBAAmB,EAAEA,mBAAoB;IACzCmB,iBAAiB,EAAElD,oBAAoB,CAACmD,QAAS;IACjDC,gBAAgB,EAAEnD,sBAAuB;IACzCoD,UAAU,EAAEzC,WAAW,CAACyC,UAAW;IACnCT,KAAK,EAAEtB,SAAU;IACjB1B,SAAS,EAAEA,SAAU;IACrBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,qBAAqB,EAAEA,qBAAsB;IAC7CwD,MAAM,EAAE3C,WAAW,KAAK,KAAM;IAC9B4C,cAAc,EAAEpD,oBAAqB;IACrCqD,UAAU,EAAEpD,gBAAiB;IAC7BqD,UAAU,EAAEnD,gBAAiB;IAC7BoD,yBAAyB,EAAErD,gBAAgB,CAAC0C,eAAgB;IAC5DY,eAAe,EAAEnD,qBAAqB,CAACoC,KAAM;IAC7CZ,oBAAoB,EAAEA,oBAAqB;IAC3C4B,kBAAkB,EAAEpD,qBAAqB,CAAC2C,QAAS;IACnDU,oBAAoB,EAAErD,qBAAqB,CAACsD,UAAW;IACvDC,oBAAoB,EAAExD,0BAA2B;IACjDa,KAAK,EACHN,WAAW,KAAK+B,SAAS,GACrB/B,WAAW,GACXM,KAAK,KAAKyB,SAAS,GACnBzB,KAAK,GACLF,KAAK,CAAC8C,IACX;IACDC,UAAU,EACRlD,gBAAgB,CAAC6B,KAAK,KAAKC,SAAS,GAChC9B,gBAAgB,CAAC6B,KAAK,GACtB/B,eAAe,KAAKgC,SAAS,GAC7BhC,eAAe,GACfQ,MAAM,CAAC6C,IACZ;IACDjC,eAAe,EAAEA,eAAgB;IACjCkC,aAAa,EAAEpD,gBAAgB,CAACoC,QAAS;IACzCiB,eAAe,EAAErD,gBAAgB,CAAC+C,UAAW;IAC7CO,eAAe,EAAErD,qBAAsB;IACvCsD,WAAW,EAAErD,iBAAiB,KAAK,IAAK;IACxCsD,UAAU,EAAE/C,cAAe;IAC3BgD,UAAU,EAAE/C;EAAe,GAC1Bf,WAAW,KAAKmC,SAAS,gBACxBlE,KAAA,CAAAmE,aAAA,CAAC3D,0BAA0B,QACxBuB,WAAW,CAAC;IAAEY;EAAU,CAAC,CACA,CAAC,GAC3B,IAAI,EACP5B,eAAe,KAAKmD,SAAS,gBAC5BlE,KAAA,CAAAmE,aAAA,CAAC/D,gCAAgC;IAC/B0F,GAAG,EAAC,WAAW;IACfC,MAAM,EAAEhF;EAAgB,CACzB,CAAC,GACA,IAAI,EACPe,UAAU,KAAKoC,SAAS,gBACvBlE,KAAA,CAAAmE,aAAA,CAAC5D,yBAAyB,QACvBuB,UAAU,CAAC;IAAEa;EAAU,CAAC,CACA,CAAC,GAC1B,IAAI,EACPpB,YAAY,KAAK2C,SAAS,gBACzBlE,KAAA,CAAAmE,aAAA,CAAC9D,2BAA2B,QACzBkB,YAAY,CAAC;IAAEoB;EAAU,CAAC,CACA,CAAC,GAC5B,IAAI,EACPzC,sCAAsC,IACvCuD,yBAAyB,KAAKS,SAAS,gBACrClE,KAAA,CAAAmE,aAAA,CAAC1D,8BAA8B,qBAE7BT,KAAA,CAAAmE,aAAA,CAACzD,SAAS,EAAK+C,yBAA4B,CACb,CAAC,GAC/B,IACmB,CAAC;AAE9B", "ignoreList": []}