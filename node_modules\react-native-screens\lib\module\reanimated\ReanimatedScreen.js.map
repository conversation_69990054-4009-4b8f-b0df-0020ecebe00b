{"version": 3, "names": ["React", "InnerScreen", "Animated", "AnimatedScreen", "createAnimatedComponent", "ReanimatedScreen", "forwardRef", "props", "ref", "createElement", "_extends", "displayName"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedScreen.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,sBAAsB;AAGlD;AACA,OAAOC,QAAQ,MAAM,yBAAyB;AAE9C,MAAMC,cAAc,GAAGD,QAAQ,CAACE,uBAAuB,CACrDH,WACF,CAAC;AAED,MAAMI,gBAAgB,gBAAGL,KAAK,CAACM,UAAU,CACvC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACd,oBACER,KAAA,CAAAS,aAAA,CAACN;EACC;EAAA,EAAAO,QAAA;IACAF,GAAG,EAAEA;EAAI,GACLD,KAAK,CACV,CAAC;AAEN,CACF,CAAC;AAEDF,gBAAgB,CAACM,WAAW,GAAG,kBAAkB;AAEjD,eAAeN,gBAAgB", "ignoreList": []}