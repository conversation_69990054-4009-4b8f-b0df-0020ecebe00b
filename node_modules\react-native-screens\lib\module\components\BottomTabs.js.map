{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "BottomTabsNativeComponent", "findNodeHandle", "StyleSheet", "featureFlags", "BottomTabs", "props", "console", "info", "onNativeFocusChange", "experimentalControlNavigationStateInJS", "experiment", "controlledBottomTabs", "filteredProps", "componentNodeRef", "useRef", "componentNodeHandle", "useEffect", "current", "onNativeFocusChangeCallback", "useCallback", "event", "log", "JSON", "stringify", "nativeEvent", "createElement", "style", "styles", "fillParent", "controlNavigationStateInJS", "ref", "children", "create", "flex", "width", "height"], "sourceRoot": "../../../src", "sources": ["components/BottomTabs.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AACzB,OAAOC,yBAAyB,MAIzB,qCAAqC;AAC5C,SAEEC,cAAc,EAEdC,UAAU,QAGL,cAAc;AACrB,OAAOC,YAAY,MAAM,UAAU;AAuCnC;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAsB,EAAE;EAC1CC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;EAEjC,MAAM;IACJC,mBAAmB;IACnBC,sCAAsC,GAAGN,YAAY,CAACO,UAAU,CAC7DC,oBAAoB;IACvB,GAAGC;EACL,CAAC,GAAGP,KAAK;EAET,MAAMQ,gBAAgB,GACpBd,KAAK,CAACe,MAAM,CAAkD,IAAI,CAAC;EACrE,MAAMC,mBAAmB,GAAGhB,KAAK,CAACe,MAAM,CAAS,CAAC,CAAC,CAAC;EAEpDf,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIH,gBAAgB,CAACI,OAAO,IAAI,IAAI,EAAE;MACpCF,mBAAmB,CAACE,OAAO,GACzBhB,cAAc,CAACY,gBAAgB,CAACI,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,MAAM;MACLF,mBAAmB,CAACE,OAAO,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,2BAA2B,GAAGnB,KAAK,CAACoB,WAAW,CAClDC,KAAmD,IAAK;IACvDd,OAAO,CAACe,GAAG,CACT,eACEN,mBAAmB,CAACE,OAAO,IAAI,CAAC,CAAC,0BACTK,IAAI,CAACC,SAAS,CAACH,KAAK,CAACI,WAAW,CAAC,EAC7D,CAAC;IACDhB,mBAAmB,GAAGY,KAAK,CAAC;EAC9B,CAAC,EACD,CAACZ,mBAAmB,CACtB,CAAC;EAED,oBACET,KAAA,CAAA0B,aAAA,CAACzB,yBAAyB,EAAAd,QAAA;IACxBwC,KAAK,EAAEC,MAAM,CAACC,UAAW;IACzBpB,mBAAmB,EAAEU,2BAA4B;IACjDW,0BAA0B,EAAEpB;IAC5B;IAAA;IACAqB,GAAG,EAAEjB;EAAiB,GAClBD,aAAa,GAChBA,aAAa,CAACmB,QACU,CAAC;AAEhC;AAEA,eAAe3B,UAAU;AAEzB,MAAMuB,MAAM,GAAGzB,UAAU,CAAC8B,MAAM,CAAC;EAC/BJ,UAAU,EAAE;IACVK,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}