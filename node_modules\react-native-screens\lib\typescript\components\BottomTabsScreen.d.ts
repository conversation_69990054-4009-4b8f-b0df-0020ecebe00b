import React from 'react';
import { BlurEffect } from '../fabric/BottomTabsScreenNativeComponent';
import { type ColorValue, type ImageSourcePropType, type NativeSyntheticEvent, TextStyle, type ViewProps } from 'react-native';
export type EmptyObject = Record<string, never>;
export type BottomTabsScreenEventHandler<T> = (event: NativeSyntheticEvent<T>) => void;
export interface SFIcon {
    sfSymbolName: string;
}
export interface ImageIcon {
    imageSource: ImageSourcePropType;
}
export interface TemplateIcon {
    templateSource: ImageSourcePropType;
}
export type Icon = SFIcon | ImageIcon | TemplateIcon;
export interface BottomTabsScreenProps {
    children?: ViewProps['children'];
    placeholder?: React.ReactNode | undefined;
    isFocused?: boolean;
    tabKey: string;
    tabBarBackgroundColor?: ColorValue;
    tabBarBlurEffect?: BlurEffect;
    tabBarItemTitleFontFamily?: TextStyle['fontFamily'];
    tabBarItemTitleFontSize?: TextStyle['fontSize'];
    tabBarItemTitleFontWeight?: TextStyle['fontWeight'];
    tabBarItemTitleFontStyle?: TextStyle['fontStyle'];
    tabBarItemTitleFontColor?: TextStyle['color'];
    tabBarItemTitlePositionAdjustment?: {
        horizontal?: number;
        vertical?: number;
    };
    tabBarItemIconColor?: ColorValue;
    tabBarItemBadgeBackgroundColor?: ColorValue;
    title?: string;
    iconResourceName?: string;
    icon?: Icon;
    selectedIcon?: Icon;
    badgeValue?: string;
    specialEffects?: {
        repeatedTabSelection?: {
            popToRoot?: boolean;
            scrollToTop?: boolean;
        };
    };
    overrideScrollViewContentInsetAdjustmentBehavior?: boolean;
    onWillAppear?: BottomTabsScreenEventHandler<EmptyObject>;
    onDidAppear?: BottomTabsScreenEventHandler<EmptyObject>;
    onWillDisappear?: BottomTabsScreenEventHandler<EmptyObject>;
    onDidDisappear?: BottomTabsScreenEventHandler<EmptyObject>;
}
/**
 * EXPERIMENTAL API, MIGHT CHANGE W/O ANY NOTICE
 */
declare function BottomTabsScreen(props: BottomTabsScreenProps): React.JSX.Element;
export default BottomTabsScreen;
//# sourceMappingURL=BottomTabsScreen.d.ts.map