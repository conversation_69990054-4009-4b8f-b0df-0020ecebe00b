{"version": 3, "names": ["RNS_CONTROLLED_BOTTOM_TABS_DEFAULT", "compatibilityFlags", "exports", "isNewBackTitleImplementation", "usesHeaderFlexboxImplementation", "_featureFlags", "experiment", "controlledBottomTabs", "stable", "featureFlags", "value", "console", "error", "_default", "default"], "sourceRoot": "../../src", "sources": ["flags.ts"], "mappings": ";;;;;;AAAA,MAAMA,kCAAkC,GAAG,IAAI;;AAE/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG;EAChC;AACF;AACA;AACA;AACA;AACA;EACEE,4BAA4B,EAAE,IAAI;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,+BAA+B,EAAE;AACnC,CAAU;AAEV,MAAMC,aAAa,GAAG;EACpBC,UAAU,EAAE;IACVC,oBAAoB,EAAEP;EACxB,CAAC;EACDQ,MAAM,EAAE,CAAC;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,MAAMC,YAAY,GAAAP,OAAA,CAAAO,YAAA,GAAG;EAC1B;AACF;AACA;EACEH,UAAU,EAAE;IACV,IAAIC,oBAAoBA,CAAA,EAAG;MACzB,OAAOF,aAAa,CAACC,UAAU,CAACC,oBAAoB;IACtD,CAAC;IACD,IAAIA,oBAAoBA,CAACG,KAAc,EAAE;MACvC,IACEA,KAAK,KAAKL,aAAa,CAACC,UAAU,CAACC,oBAAoB,IACvDF,aAAa,CAACC,UAAU,CAACC,oBAAoB,KAC3CP,kCAAkC,EACpC;QACAW,OAAO,CAACC,KAAK,CACX,iHACF,CAAC;MACH;MACAP,aAAa,CAACC,UAAU,CAACC,oBAAoB,GAAGG,KAAK;IACvD;EACF,CAAC;EACD;AACF;AACA;EACEF,MAAM,EAAE,CAAC;AACX,CAAC;AAAC,IAAAK,QAAA,GAAAX,OAAA,CAAAY,OAAA,GAEaL,YAAY", "ignoreList": []}