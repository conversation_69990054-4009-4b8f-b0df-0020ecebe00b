/// <reference types="react-native/types/modules/Codegen" />
import type { ColorValue, ViewProps } from 'react-native';
import type { DirectEventHandler, Float, WithDefault } from 'react-native/Libraries/Types/CodegenTypes';
export type NativeFocusChangeEvent = {
    tabKey: string;
};
export type BlurEffect = 'none' | 'extraLight' | 'light' | 'dark' | 'regular' | 'prominent' | 'systemUltraThinMaterial' | 'systemThinMaterial' | 'systemMaterial' | 'systemThickMaterial' | 'systemChromeMaterial' | 'systemUltraThinMaterialLight' | 'systemThinMaterialLight' | 'systemMaterialLight' | 'systemThickMaterialLight' | 'systemChromeMaterialLight' | 'systemUltraThinMaterialDark' | 'systemThinMaterialDark' | 'systemMaterialDark' | 'systemThickMaterialDark' | 'systemChromeMaterialDark';
export interface NativeProps extends ViewProps {
    onNativeFocusChange?: DirectEventHandler<NativeFocusChangeEvent>;
    tabBarBackgroundColor?: ColorValue;
    tabBarBlurEffect?: WithDefault<BlurEffect, 'none'>;
    tabBarTintColor?: ColorValue;
    tabBarItemTitleFontFamily?: string;
    tabBarItemTitleFontSize?: Float;
    tabBarItemTitleFontWeight?: string;
    tabBarItemTitleFontStyle?: string;
    tabBarItemTitleFontColor?: ColorValue;
    tabBarItemTitlePositionAdjustment?: {
        horizontal?: Float;
        vertical?: Float;
    };
    tabBarItemIconColor?: ColorValue;
    tabBarItemBadgeBackgroundColor?: ColorValue;
    tabBarItemTitleFontColorActive?: ColorValue;
    tabBarItemIconColorActive?: ColorValue;
    tabBarItemTitleFontSizeActive?: Float;
    controlNavigationStateInJS?: WithDefault<boolean, false>;
}
declare const _default: import("react-native/Libraries/Utilities/codegenNativeComponent").NativeComponentType<NativeProps>;
export default _default;
//# sourceMappingURL=BottomTabsNativeComponent.d.ts.map