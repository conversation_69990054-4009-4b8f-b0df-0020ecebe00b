import SecurityService from './SecurityService';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration (replace with your actual values)
const SUPABASE_URL = 'your-supabase-url';
const SUPABASE_ANON_KEY = 'your-supabase-anon-key';

/**
 * Advanced Authentication Service for Africa Social
 * Implements secure authentication with multiple layers of protection
 */
class AuthService {
  constructor() {
    this.supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    this.currentUser = null;
    this.authListeners = [];
  }

  // ==================== USER REGISTRATION ====================

  /**
   * Register new user with enhanced security
   */
  async register(userData) {
    try {
      const { email, password, fullName, username } = userData;
      
      // Generate device fingerprint
      const deviceFingerprint = await SecurityService.generateDeviceFingerprint();
      if (!deviceFingerprint) {
        throw new Error('Device security check failed');
      }

      // Check if device is compromised
      if (deviceFingerprint.deviceInfo.isRooted) {
        await SecurityService.logSecurityEvent('registration_blocked_rooted_device', { email });
        throw new Error('Registration not allowed on compromised devices');
      }

      // Validate password strength
      const passwordValidation = this.validatePasswordStrength(password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password requirements not met: ${passwordValidation.errors.join(', ')}`);
      }

      // Create user account
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            username,
            device_fingerprint: deviceFingerprint.fingerprint,
            registration_device: deviceFingerprint.deviceInfo,
            verification_eligible: true, // Free verification for all users
            created_at: new Date().toISOString()
          }
        }
      });

      if (error) {
        await SecurityService.logSecurityEvent('registration_failed', { 
          email, 
          error: error.message,
          deviceFingerprint: deviceFingerprint.fingerprint 
        });
        throw error;
      }

      // Log successful registration
      await SecurityService.logSecurityEvent('user_registered', {
        userId: data.user.id,
        email,
        deviceFingerprint: deviceFingerprint.fingerprint
      });

      // Store user data securely
      await SecurityService.storeSecurely('user_registration_data', {
        userId: data.user.id,
        email,
        fullName,
        username,
        deviceFingerprint
      });

      return {
        success: true,
        user: data.user,
        needsEmailVerification: !data.user.email_confirmed_at
      };

    } catch (error) {
      console.error('Registration failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== USER LOGIN ====================

  /**
   * Login user with advanced security checks
   */
  async login(email, password, options = {}) {
    try {
      // Generate device fingerprint
      const deviceFingerprint = await SecurityService.generateDeviceFingerprint();
      if (!deviceFingerprint) {
        throw new Error('Device security check failed');
      }

      // Check for suspicious activity
      const suspiciousActivity = await SecurityService.detectSuspiciousActivity(
        email, 
        deviceFingerprint
      );

      if (suspiciousActivity.suspicious) {
        if (suspiciousActivity.reason === 'Too many failed login attempts') {
          throw new Error(`Account temporarily locked. Try again after ${suspiciousActivity.lockoutUntil}`);
        }
        
        if (suspiciousActivity.requiresVerification) {
          // In a real app, you'd send verification email/SMS
          await SecurityService.logSecurityEvent('new_device_login_attempt', {
            email,
            deviceFingerprint: deviceFingerprint.fingerprint
          });
        }
      }

      // Attempt login
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      });

      // Log login attempt
      await SecurityService.logLoginAttempt(
        email,
        !error,
        deviceFingerprint,
        { loginMethod: 'password' }
      );

      if (error) {
        await SecurityService.logSecurityEvent('login_failed', {
          email,
          error: error.message,
          deviceFingerprint: deviceFingerprint.fingerprint
        });
        throw error;
      }

      // Create secure session
      const session = await SecurityService.createSession(
        data.user.id,
        deviceFingerprint
      );

      if (!session) {
        throw new Error('Failed to create secure session');
      }

      // Store authentication data securely
      await SecurityService.storeSecurely('auth_tokens', {
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at
      }, { requireAuth: true });

      // Update current user
      this.currentUser = data.user;
      this.notifyAuthListeners('login', data.user);

      await SecurityService.logSecurityEvent('login_successful', {
        userId: data.user.id,
        email,
        sessionId: session.sessionId
      });

      return {
        success: true,
        user: data.user,
        session: data.session,
        requiresBiometricSetup: options.enableBiometric && !(await SecurityService.isBiometricAvailable()).available
      };

    } catch (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== BIOMETRIC LOGIN ====================

  /**
   * Login with biometric authentication
   */
  async loginWithBiometric() {
    try {
      // Check if biometric is available
      const biometricStatus = await SecurityService.isBiometricAvailable();
      if (!biometricStatus.available) {
        throw new Error('Biometric authentication not available');
      }

      // Authenticate with biometric
      const biometricResult = await SecurityService.authenticateWithBiometrics(
        'Login to Africa Social'
      );

      if (!biometricResult.success) {
        throw new Error(biometricResult.error || 'Biometric authentication failed');
      }

      // Retrieve stored credentials
      const storedAuth = await SecurityService.getSecurely('auth_tokens', {
        requireAuthentication: true,
        authenticationPrompt: 'Access your saved login'
      });

      if (!storedAuth) {
        throw new Error('No saved authentication found');
      }

      // Validate session
      const sessionValidation = await SecurityService.validateSession();
      if (!sessionValidation.valid) {
        // Session expired, need to re-authenticate
        throw new Error('Session expired, please login again');
      }

      // Set current session with Supabase
      const { data, error } = await this.supabase.auth.setSession({
        access_token: storedAuth.accessToken,
        refresh_token: storedAuth.refreshToken
      });

      if (error) {
        throw error;
      }

      this.currentUser = data.user;
      this.notifyAuthListeners('biometric_login', data.user);

      await SecurityService.logSecurityEvent('biometric_login_successful', {
        userId: data.user.id
      });

      return {
        success: true,
        user: data.user,
        session: data.session
      };

    } catch (error) {
      console.error('Biometric login failed:', error);
      await SecurityService.logSecurityEvent('biometric_login_failed', {
        error: error.message
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== LOGOUT ====================

  /**
   * Secure logout with cleanup
   */
  async logout() {
    try {
      // Invalidate session
      await SecurityService.invalidateSession();

      // Sign out from Supabase
      const { error } = await this.supabase.auth.signOut();
      if (error) {
        console.error('Supabase logout error:', error);
      }

      // Clear sensitive data
      await SecurityService.deleteSecurely('auth_tokens');
      
      // Update current user
      this.currentUser = null;
      this.notifyAuthListeners('logout', null);

      await SecurityService.logSecurityEvent('user_logged_out');

      return { success: true };

    } catch (error) {
      console.error('Logout failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ==================== PASSWORD MANAGEMENT ====================

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const errors = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common passwords
    const commonPasswords = [
      'password', '123456', 'password123', 'admin', 'qwerty',
      'letmein', 'welcome', 'monkey', '1234567890'
    ];
    
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common, please choose a stronger password');
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength: this.calculatePasswordStrength(password)
    };
  }

  /**
   * Calculate password strength score
   */
  calculatePasswordStrength(password) {
    let score = 0;
    
    // Length bonus
    score += Math.min(password.length * 2, 20);
    
    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/\d/.test(password)) score += 5;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    
    // Penalty for repeated characters
    const repeatedChars = password.match(/(.)\1{2,}/g);
    if (repeatedChars) score -= repeatedChars.length * 5;
    
    // Normalize to 0-100 scale
    score = Math.max(0, Math.min(100, score));
    
    if (score < 30) return 'weak';
    if (score < 60) return 'medium';
    if (score < 80) return 'strong';
    return 'very_strong';
  }

  // ==================== SESSION MANAGEMENT ====================

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated() {
    try {
      const sessionValidation = await SecurityService.validateSession();
      return sessionValidation.valid;
    } catch (error) {
      return false;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken() {
    try {
      const { data, error } = await this.supabase.auth.refreshSession();
      
      if (error) {
        throw error;
      }

      // Update stored tokens
      await SecurityService.storeSecurely('auth_tokens', {
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at
      }, { requireAuth: true });

      return { success: true, session: data.session };

    } catch (error) {
      console.error('Token refresh failed:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== AUTH LISTENERS ====================

  /**
   * Add authentication state listener
   */
  addAuthListener(callback) {
    this.authListeners.push(callback);
    return () => {
      this.authListeners = this.authListeners.filter(listener => listener !== callback);
    };
  }

  /**
   * Notify authentication listeners
   */
  notifyAuthListeners(event, user) {
    this.authListeners.forEach(callback => {
      try {
        callback(event, user);
      } catch (error) {
        console.error('Auth listener error:', error);
      }
    });
  }

  // ==================== VERIFICATION SYSTEM ====================

  /**
   * Request free verification badge
   */
  async requestVerification(userId, verificationData = {}) {
    try {
      // In Africa Social, verification is free for all users
      const verificationRequest = {
        userId,
        requestedAt: new Date().toISOString(),
        status: 'approved', // Auto-approve for free verification
        verificationData,
        approvedAt: new Date().toISOString(),
        badgeType: 'free_verification'
      };

      // Store verification status
      await SecurityService.storeSecurely(`verification_${userId}`, verificationRequest);

      await SecurityService.logSecurityEvent('verification_granted', {
        userId,
        badgeType: 'free_verification'
      });

      return {
        success: true,
        verified: true,
        badgeType: 'free_verification',
        message: 'Congratulations! Your free verification badge has been granted.'
      };

    } catch (error) {
      console.error('Verification request failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check verification status
   */
  async getVerificationStatus(userId) {
    try {
      const verification = await SecurityService.getSecurely(`verification_${userId}`);
      return verification || { verified: false };
    } catch (error) {
      console.error('Failed to get verification status:', error);
      return { verified: false };
    }
  }
}

export default new AuthService();
