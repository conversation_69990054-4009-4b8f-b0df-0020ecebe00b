import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SIZES } from '../constants/theme';
import { useSecurity } from '../contexts/SecurityContext';

const SecurityDashboard = ({ visible, onClose }) => {
  const [securityLogs, setSecurityLogs] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const {
    securityLevel,
    securityScore,
    deviceSecure,
    biometricAvailable,
    canUseBiometric,
    sessionValid,
    verificationStatus,
    getSecurityLogs,
    setupBiometric,
    refreshSecurityStatus
  } = useSecurity();

  useEffect(() => {
    if (visible) {
      loadSecurityLogs();
    }
  }, [visible]);

  const loadSecurityLogs = async () => {
    try {
      const logs = await getSecurityLogs();
      setSecurityLogs(logs.slice(-10)); // Show last 10 events
    } catch (error) {
      console.error('Failed to load security logs:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshSecurityStatus();
      await loadSecurityLogs();
    } catch (error) {
      Alert.alert('Error', 'Failed to refresh security status');
    } finally {
      setRefreshing(false);
    }
  };

  const handleSetupBiometric = async () => {
    const result = await setupBiometric();
    if (result.success) {
      Alert.alert(
        'Biometric Setup Complete',
        'Biometric authentication has been successfully configured for your account.'
      );
      await handleRefresh();
    } else {
      Alert.alert('Setup Failed', result.error || 'Failed to setup biometric authentication');
    }
  };

  const getSecurityColor = () => {
    switch (securityLevel) {
      case 'maximum': return COLORS.success;
      case 'high': return COLORS.primary;
      case 'medium': return COLORS.warning;
      default: return COLORS.error;
    }
  };

  const getSecurityIcon = () => {
    switch (securityLevel) {
      case 'maximum': return 'shield-checkmark';
      case 'high': return 'shield';
      case 'medium': return 'shield-half';
      default: return 'warning';
    }
  };

  const formatLogTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString() + ' ' + date.toLocaleDateString();
  };

  const getLogIcon = (eventType) => {
    switch (eventType) {
      case 'login_successful': return 'log-in';
      case 'biometric_auth_success': return 'finger-print';
      case 'session_created': return 'key';
      case 'verification_granted': return 'checkmark-circle';
      case 'security_data_cleared': return 'trash';
      case 'api_request': return 'cloud';
      default: return 'information-circle';
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Security Dashboard</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Security Overview */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Security Overview</Text>
            
            <View style={[styles.securityCard, { borderLeftColor: getSecurityColor() }]}>
              <View style={styles.securityHeader}>
                <Ionicons name={getSecurityIcon()} size={24} color={getSecurityColor()} />
                <Text style={[styles.securityLevel, { color: getSecurityColor() }]}>
                  {securityLevel.toUpperCase()} SECURITY
                </Text>
                <Text style={styles.securityScore}>{securityScore}%</Text>
              </View>
              
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { 
                      width: `${securityScore}%`,
                      backgroundColor: getSecurityColor()
                    }
                  ]} 
                />
              </View>
            </View>
          </View>

          {/* Security Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Security Features</Text>
            
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <Ionicons 
                  name={deviceSecure ? "checkmark-circle" : "close-circle"} 
                  size={20} 
                  color={deviceSecure ? COLORS.success : COLORS.error} 
                />
                <Text style={styles.featureText}>Device Security</Text>
                <Text style={[styles.featureStatus, { 
                  color: deviceSecure ? COLORS.success : COLORS.error 
                }]}>
                  {deviceSecure ? 'Secure' : 'Compromised'}
                </Text>
              </View>

              <View style={styles.featureItem}>
                <Ionicons 
                  name={biometricAvailable ? "checkmark-circle" : "close-circle"} 
                  size={20} 
                  color={biometricAvailable ? COLORS.success : COLORS.gray} 
                />
                <Text style={styles.featureText}>Biometric Authentication</Text>
                <Text style={[styles.featureStatus, { 
                  color: biometricAvailable ? COLORS.success : COLORS.gray 
                }]}>
                  {biometricAvailable ? 'Available' : 'Not Available'}
                </Text>
              </View>

              <View style={styles.featureItem}>
                <Ionicons 
                  name={sessionValid ? "checkmark-circle" : "close-circle"} 
                  size={20} 
                  color={sessionValid ? COLORS.success : COLORS.error} 
                />
                <Text style={styles.featureText}>Secure Session</Text>
                <Text style={[styles.featureStatus, { 
                  color: sessionValid ? COLORS.success : COLORS.error 
                }]}>
                  {sessionValid ? 'Active' : 'Invalid'}
                </Text>
              </View>

              <View style={styles.featureItem}>
                <Ionicons 
                  name={verificationStatus.verified ? "checkmark-circle" : "close-circle"} 
                  size={20} 
                  color={verificationStatus.verified ? COLORS.verificationGreen : COLORS.gray} 
                />
                <Text style={styles.featureText}>Account Verification</Text>
                <Text style={[styles.featureStatus, { 
                  color: verificationStatus.verified ? COLORS.verificationGreen : COLORS.gray 
                }]}>
                  {verificationStatus.verified ? 'Verified' : 'Not Verified'}
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Security Actions</Text>
            
            <View style={styles.actionButtons}>
              {!canUseBiometric && biometricAvailable && (
                <TouchableOpacity 
                  style={styles.actionButton}
                  onPress={handleSetupBiometric}
                >
                  <Ionicons name="finger-print" size={20} color={COLORS.white} />
                  <Text style={styles.actionButtonText}>Setup Biometric</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={handleRefresh}
                disabled={refreshing}
              >
                <Ionicons 
                  name={refreshing ? "sync" : "refresh"} 
                  size={20} 
                  color={COLORS.primary} 
                />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                  {refreshing ? 'Refreshing...' : 'Refresh Status'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Security Logs */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Security Events</Text>
            
            <View style={styles.logsList}>
              {securityLogs.length > 0 ? (
                securityLogs.map((log, index) => (
                  <View key={index} style={styles.logItem}>
                    <Ionicons 
                      name={getLogIcon(log.eventType)} 
                      size={16} 
                      color={COLORS.primary} 
                    />
                    <View style={styles.logContent}>
                      <Text style={styles.logEvent}>
                        {log.eventType.replace(/_/g, ' ').toUpperCase()}
                      </Text>
                      <Text style={styles.logTime}>
                        {formatLogTime(log.timestamp)}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={styles.noLogsText}>No security events recorded</Text>
              )}
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: 12,
  },
  securityCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityLevel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  securityScore: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  progressBar: {
    height: 8,
    backgroundColor: COLORS.lightGray,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  featuresList: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  featureText: {
    fontSize: 14,
    color: COLORS.textPrimary,
    marginLeft: 12,
    flex: 1,
  },
  featureStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  secondaryButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.white,
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: COLORS.primary,
  },
  logsList: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
  },
  logItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  logContent: {
    marginLeft: 12,
    flex: 1,
  },
  logEvent: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  logTime: {
    fontSize: 10,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  noLogsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default SecurityDashboard;
