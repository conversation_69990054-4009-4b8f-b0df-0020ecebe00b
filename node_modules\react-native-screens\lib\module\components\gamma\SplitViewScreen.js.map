{"version": 3, "names": ["React", "StyleSheet", "SplitViewScreenNativeComponent", "SplitViewScreen", "children", "createElement", "style", "absoluteFill"], "sourceRoot": "../../../../src", "sources": ["components/gamma/SplitViewScreen.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,cAAc;AAEzC,OAAOC,8BAA8B,MAAM,mDAAmD;AAW9F;AACA;AACA;AACA,SAASC,eAAeA,CAAC;EAAEC;AAA+B,CAAC,EAAE;EAC3D,oBACEJ,KAAA,CAAAK,aAAA,CAACH,8BAA8B;IAACI,KAAK,EAAEL,UAAU,CAACM;EAAa,GAC5DH,QAC6B,CAAC;AAErC;AAEA,eAAeD,eAAe", "ignoreList": []}