/// <reference types="react-native/types/modules/Codegen" />
import type { ViewProps } from 'react-native';
import type { WithDefault } from 'react-native/Libraries/Types/CodegenTypes';
export type SplitViewSplitBehavior = 'automatic' | 'displace' | 'overlay' | 'tile';
export type SplitViewPrimaryEdge = 'leading' | 'trailing';
export type SplitViewDisplayMode = 'automatic' | 'secondaryOnly' | 'oneBesideSecondary' | 'oneOverSecondary' | 'twoBesideSecondary' | 'twoOverSecondary' | 'twoDisplaceSecondary';
export interface NativeProps extends ViewProps {
    displayMode?: WithDefault<SplitViewDisplayMode, 'automatic'>;
    splitBehavior?: WithDefault<SplitViewSplitBehavior, 'automatic'>;
    primaryEdge?: WithDefault<SplitViewPrimaryEdge, 'leading'>;
    showSecondaryToggleButton?: WithDefault<boolean, false>;
    presentsWithGesture?: WithDefault<boolean, true>;
}
declare const _default: import("react-native/Libraries/Utilities/codegenNativeComponent").NativeComponentType<NativeProps>;
export default _default;
//# sourceMappingURL=SplitViewHostNativeComponent.d.ts.map