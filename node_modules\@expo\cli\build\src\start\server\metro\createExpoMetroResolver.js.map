{"version": 3, "sources": ["../../../../../src/start/server/metro/createExpoMetroResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { Resolution, ResolutionContext } from 'metro-resolver';\nimport path from 'path';\n\nimport jestResolver from './createJResolver';\nimport { isNodeExternal } from './externals';\nimport { formatFileCandidates } from './formatFileCandidates';\nimport { isServerEnvironment } from '../middleware/metroOptions';\n\nexport class FailedToResolvePathError extends Error {\n  // Added to ensure the error is matched by our tooling.\n  // TODO: Test that this matches `isFailedToResolvePathError`\n  candidates = {};\n}\n\nclass ShimModuleError extends Error {}\n\nconst debug = require('debug')('expo:metro:resolve') as typeof console.log;\n\nconst realpathFS =\n  process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function'\n    ? fs.realpathSync.native\n    : fs.realpathSync;\n\nfunction realpathSync(x: string) {\n  try {\n    return realpathFS(x);\n  } catch (realpathErr: any) {\n    if (realpathErr.code !== 'ENOENT') {\n      throw realpathErr;\n    }\n  }\n  return x;\n}\n\nexport function createFastResolver({\n  preserveSymlinks,\n  blockList,\n}: {\n  preserveSymlinks: boolean;\n  blockList: RegExp[];\n}) {\n  debug('Creating with settings:', { preserveSymlinks, blockList });\n  const cachedExtensions: Map<string, readonly string[]> = new Map();\n\n  function getAdjustedExtensions({\n    metroSourceExtensions,\n    platform,\n    isNative,\n  }: {\n    metroSourceExtensions: readonly string[];\n    platform: string | null;\n    isNative: boolean;\n  }): readonly string[] {\n    const key = JSON.stringify({ metroSourceExtensions, platform, isNative });\n    if (cachedExtensions.has(key)) {\n      return cachedExtensions.get(key)!;\n    }\n\n    let output = metroSourceExtensions;\n    if (platform) {\n      const nextOutput: string[] = [];\n\n      output.forEach((ext) => {\n        nextOutput.push(`${platform}.${ext}`);\n        if (isNative) {\n          nextOutput.push(`native.${ext}`);\n        }\n        nextOutput.push(ext);\n      });\n\n      output = nextOutput;\n    }\n\n    output = Array.from(new Set<string>(output));\n\n    // resolve expects these to start with a dot.\n    output = output.map((ext) => `.${ext}`);\n\n    cachedExtensions.set(key, output);\n\n    return output;\n  }\n\n  function fastResolve(\n    context: Pick<\n      ResolutionContext,\n      | 'unstable_enablePackageExports'\n      | 'customResolverOptions'\n      | 'sourceExts'\n      | 'preferNativePlatform'\n      | 'originModulePath'\n      | 'getPackage'\n      | 'nodeModulesPaths'\n      | 'mainFields'\n      | 'resolveAsset'\n      | 'unstable_conditionNames'\n      | 'unstable_conditionsByPlatform'\n      | 'fileSystemLookup'\n      | 'isESMImport'\n    >,\n    moduleName: string,\n    platform: string | null\n  ): Resolution {\n    const environment = context.customResolverOptions?.environment;\n    const isServer = isServerEnvironment(environment);\n\n    const extensions = getAdjustedExtensions({\n      metroSourceExtensions: context.sourceExts,\n      platform,\n      isNative: context.preferNativePlatform,\n    }) as string[];\n\n    let fp: string;\n\n    const conditions = context.unstable_enablePackageExports\n      ? [\n          ...new Set([\n            'default',\n            context.isESMImport === true ? 'import' : 'require',\n            ...context.unstable_conditionNames,\n            ...(platform != null ? (context.unstable_conditionsByPlatform[platform] ?? []) : []),\n          ]),\n        ]\n      : [];\n\n    // NOTE(cedric): metro@0.81.0 ships with `fileSystemLookup`, while `metro@0.80.12` ships as unstable\n    const fileSystemLookup = (\n      'unstable_fileSystemLookup' in context\n        ? context.unstable_fileSystemLookup\n        : context.fileSystemLookup\n    ) as ResolutionContext['fileSystemLookup'] | undefined;\n\n    if (!fileSystemLookup) {\n      throw new Error('Metro API fileSystemLookup is required for fast resolver');\n    }\n\n    try {\n      fp = jestResolver(moduleName, {\n        blockList,\n        enablePackageExports: context.unstable_enablePackageExports,\n        basedir: path.dirname(context.originModulePath),\n        paths: context.nodeModulesPaths.length ? (context.nodeModulesPaths as string[]) : undefined,\n        extensions,\n        conditions,\n        realpathSync(file: string): string {\n          let metroRealPath: string | null = null;\n\n          const res = fileSystemLookup(file);\n          if (res?.exists) {\n            metroRealPath = res.realPath;\n          }\n\n          if (metroRealPath == null && preserveSymlinks) {\n            return realpathSync(file);\n          }\n          return metroRealPath ?? file;\n        },\n        isDirectory(file: string): boolean {\n          const res = fileSystemLookup(file);\n          return res.exists && res.type === 'd';\n        },\n        isFile(file: string): boolean {\n          const res = fileSystemLookup(file);\n          return res.exists && res.type === 'f';\n        },\n        pathExists(file: string): boolean {\n          return fileSystemLookup(file).exists;\n        },\n        packageFilter(pkg) {\n          // set the pkg.main to the first available field in context.mainFields\n          for (const field of context.mainFields) {\n            if (\n              pkg[field] &&\n              // object-inspect uses browser: {} in package.json\n              typeof pkg[field] === 'string'\n            ) {\n              return {\n                ...pkg,\n                main: pkg[field],\n              };\n            }\n          }\n          return pkg;\n        },\n        // Used to ensure files trace to packages instead of node_modules in expo/expo. This is how Metro works and\n        // the app doesn't finish without it.\n        preserveSymlinks,\n        readPackageSync(readFileSync, pkgFile) {\n          return context.getPackage(pkgFile) ?? JSON.parse(fs.readFileSync(pkgFile, 'utf8'));\n        },\n        includeCoreModules: isServer,\n\n        pathFilter:\n          // Disable `browser` field for server environments.\n          isServer\n            ? undefined\n            : // Enable `browser` field support\n              (pkg: any, _resolvedPath: string, relativePathIn: string): string => {\n                let relativePath = relativePathIn;\n                if (relativePath[0] !== '.') {\n                  relativePath = `./${relativePath}`;\n                }\n\n                const replacements = pkg.browser;\n                if (replacements === undefined) {\n                  return '';\n                }\n\n                // TODO: Probably use a better extension matching system here.\n                // This was added for `uuid/v4` -> `./lib/rng` -> `./lib/rng-browser.js`\n                const mappedPath = replacements[relativePath] ?? replacements[relativePath + '.js'];\n                if (mappedPath === false) {\n                  throw new ShimModuleError();\n                }\n                return mappedPath;\n              },\n      });\n    } catch (error: any) {\n      if (error instanceof ShimModuleError) {\n        return {\n          type: 'empty',\n        };\n      }\n\n      if ('code' in error && error.code === 'MODULE_NOT_FOUND') {\n        if (isNodeExternal(moduleName)) {\n          // In this case, mock the file to use an empty module.\n          return {\n            type: 'empty',\n          };\n        }\n\n        debug({ moduleName, platform, conditions, isServer, preserveSymlinks }, context);\n\n        throw new FailedToResolvePathError(\n          'The module could not be resolved because no file or module matched the pattern:\\n' +\n            `  ${formatFileCandidates(\n              {\n                type: 'sourceFile',\n                filePathPrefix: moduleName,\n                candidateExts: extensions,\n              },\n              true\n            )}\\n\\nFrom:\\n  ${context.originModulePath}\\n`\n        );\n      }\n      throw error;\n    }\n\n    if (context.sourceExts.some((ext) => fp.endsWith(ext))) {\n      return {\n        type: 'sourceFile',\n        filePath: fp,\n      };\n    }\n\n    if (isNodeExternal(fp)) {\n      if (isServer) {\n        return {\n          type: 'sourceFile',\n          filePath: fp,\n        };\n      }\n      // NOTE: This shouldn't happen, the module should throw.\n      // Mock non-server built-in modules to empty.\n      return {\n        type: 'empty',\n      };\n    }\n\n    // NOTE: platform extensions may not be supported on assets.\n\n    if (platform === 'web') {\n      // Skip multi-resolution on web/server bundles. Only consideration here is that\n      // we may still need it in case the only image is a multi-resolution image.\n      return {\n        type: 'assetFiles',\n        filePaths: [fp],\n      };\n    }\n\n    const dirPath = path.dirname(fp);\n    const extension = path.extname(fp);\n    const basename = path.basename(fp, extension);\n    return {\n      type: 'assetFiles',\n      // Support multi-resolution asset extensions...\n      filePaths: context.resolveAsset(dirPath, basename, extension) ?? [fp],\n    };\n  }\n\n  return fastResolve;\n}\n"], "names": ["FailedToResolvePathError", "createFastResolver", "Error", "candidates", "ShimModuleError", "debug", "require", "realpathFS", "process", "platform", "fs", "realpathSync", "native", "x", "realpathErr", "code", "preserveSymlinks", "blockList", "cachedExtensions", "Map", "getAdjustedExtensions", "metroSourceExtensions", "isNative", "key", "JSON", "stringify", "has", "get", "output", "nextOutput", "for<PERSON>ach", "ext", "push", "Array", "from", "Set", "map", "set", "fastResolve", "context", "moduleName", "environment", "customResolverOptions", "isServer", "isServerEnvironment", "extensions", "sourceExts", "preferNativePlatform", "fp", "conditions", "unstable_enablePackageExports", "isESMImport", "unstable_conditionNames", "unstable_conditionsByPlatform", "fileSystemLookup", "unstable_fileSystemLookup", "jestResolver", "enablePackageExports", "basedir", "path", "dirname", "originModulePath", "paths", "nodeModulesPaths", "length", "undefined", "file", "metroRealPath", "res", "exists", "realPath", "isDirectory", "type", "isFile", "pathExists", "packageFilter", "pkg", "field", "mainFields", "main", "readPackageSync", "readFileSync", "pkgFile", "getPackage", "parse", "includeCoreModules", "pathFilter", "_resolvedPath", "relativePathIn", "relativePath", "replacements", "browser", "mappedPath", "error", "isNodeExternal", "formatFileCandidates", "filePathPrefix", "candidate<PERSON><PERSON><PERSON>", "some", "endsWith", "filePath", "filePaths", "<PERSON><PERSON><PERSON>", "extension", "extname", "basename", "resolveAsset"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAUYA,wBAAwB;eAAxBA;;IA0BGC,kBAAkB;eAAlBA;;;;gEAnCD;;;;;;;gEAEE;;;;;;wEAEQ;2BACM;sCACM;8BACD;;;;;;AAE7B,MAAMD,iCAAiCE;;QAAvC,gBACL,uDAAuD;QACvD,4DAA4D;aAC5DC,aAAa,CAAC;;AAChB;AAEA,MAAMC,wBAAwBF;AAAO;AAErC,MAAMG,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,aACJC,QAAQC,QAAQ,KAAK,WAAWC,aAAE,CAACC,YAAY,IAAI,OAAOD,aAAE,CAACC,YAAY,CAACC,MAAM,KAAK,aACjFF,aAAE,CAACC,YAAY,CAACC,MAAM,GACtBF,aAAE,CAACC,YAAY;AAErB,SAASA,aAAaE,CAAS;IAC7B,IAAI;QACF,OAAON,WAAWM;IACpB,EAAE,OAAOC,aAAkB;QACzB,IAAIA,YAAYC,IAAI,KAAK,UAAU;YACjC,MAAMD;QACR;IACF;IACA,OAAOD;AACT;AAEO,SAASZ,mBAAmB,EACjCe,gBAAgB,EAChBC,SAAS,EAIV;IACCZ,MAAM,2BAA2B;QAAEW;QAAkBC;IAAU;IAC/D,MAAMC,mBAAmD,IAAIC;IAE7D,SAASC,sBAAsB,EAC7BC,qBAAqB,EACrBZ,QAAQ,EACRa,QAAQ,EAKT;QACC,MAAMC,MAAMC,KAAKC,SAAS,CAAC;YAAEJ;YAAuBZ;YAAUa;QAAS;QACvE,IAAIJ,iBAAiBQ,GAAG,CAACH,MAAM;YAC7B,OAAOL,iBAAiBS,GAAG,CAACJ;QAC9B;QAEA,IAAIK,SAASP;QACb,IAAIZ,UAAU;YACZ,MAAMoB,aAAuB,EAAE;YAE/BD,OAAOE,OAAO,CAAC,CAACC;gBACdF,WAAWG,IAAI,CAAC,GAAGvB,SAAS,CAAC,EAAEsB,KAAK;gBACpC,IAAIT,UAAU;oBACZO,WAAWG,IAAI,CAAC,CAAC,OAAO,EAAED,KAAK;gBACjC;gBACAF,WAAWG,IAAI,CAACD;YAClB;YAEAH,SAASC;QACX;QAEAD,SAASK,MAAMC,IAAI,CAAC,IAAIC,IAAYP;QAEpC,6CAA6C;QAC7CA,SAASA,OAAOQ,GAAG,CAAC,CAACL,MAAQ,CAAC,CAAC,EAAEA,KAAK;QAEtCb,iBAAiBmB,GAAG,CAACd,KAAKK;QAE1B,OAAOA;IACT;IAEA,SAASU,YACPC,OAeC,EACDC,UAAkB,EAClB/B,QAAuB;YAEH8B;QAApB,MAAME,eAAcF,iCAAAA,QAAQG,qBAAqB,qBAA7BH,+BAA+BE,WAAW;QAC9D,MAAME,WAAWC,IAAAA,iCAAmB,EAACH;QAErC,MAAMI,aAAazB,sBAAsB;YACvCC,uBAAuBkB,QAAQO,UAAU;YACzCrC;YACAa,UAAUiB,QAAQQ,oBAAoB;QACxC;QAEA,IAAIC;QAEJ,MAAMC,aAAaV,QAAQW,6BAA6B,GACpD;eACK,IAAIf,IAAI;gBACT;gBACAI,QAAQY,WAAW,KAAK,OAAO,WAAW;mBACvCZ,QAAQa,uBAAuB;mBAC9B3C,YAAY,OAAQ8B,QAAQc,6BAA6B,CAAC5C,SAAS,IAAI,EAAE,GAAI,EAAE;aACpF;SACF,GACD,EAAE;QAEN,oGAAoG;QACpG,MAAM6C,mBACJ,+BAA+Bf,UAC3BA,QAAQgB,yBAAyB,GACjChB,QAAQe,gBAAgB;QAG9B,IAAI,CAACA,kBAAkB;YACrB,MAAM,IAAIpD,MAAM;QAClB;QAEA,IAAI;YACF8C,KAAKQ,IAAAA,wBAAY,EAAChB,YAAY;gBAC5BvB;gBACAwC,sBAAsBlB,QAAQW,6BAA6B;gBAC3DQ,SAASC,eAAI,CAACC,OAAO,CAACrB,QAAQsB,gBAAgB;gBAC9CC,OAAOvB,QAAQwB,gBAAgB,CAACC,MAAM,GAAIzB,QAAQwB,gBAAgB,GAAgBE;gBAClFpB;gBACAI;gBACAtC,cAAauD,IAAY;oBACvB,IAAIC,gBAA+B;oBAEnC,MAAMC,MAAMd,iBAAiBY;oBAC7B,IAAIE,uBAAAA,IAAKC,MAAM,EAAE;wBACfF,gBAAgBC,IAAIE,QAAQ;oBAC9B;oBAEA,IAAIH,iBAAiB,QAAQnD,kBAAkB;wBAC7C,OAAOL,aAAauD;oBACtB;oBACA,OAAOC,iBAAiBD;gBAC1B;gBACAK,aAAYL,IAAY;oBACtB,MAAME,MAAMd,iBAAiBY;oBAC7B,OAAOE,IAAIC,MAAM,IAAID,IAAII,IAAI,KAAK;gBACpC;gBACAC,QAAOP,IAAY;oBACjB,MAAME,MAAMd,iBAAiBY;oBAC7B,OAAOE,IAAIC,MAAM,IAAID,IAAII,IAAI,KAAK;gBACpC;gBACAE,YAAWR,IAAY;oBACrB,OAAOZ,iBAAiBY,MAAMG,MAAM;gBACtC;gBACAM,eAAcC,GAAG;oBACf,sEAAsE;oBACtE,KAAK,MAAMC,SAAStC,QAAQuC,UAAU,CAAE;wBACtC,IACEF,GAAG,CAACC,MAAM,IACV,kDAAkD;wBAClD,OAAOD,GAAG,CAACC,MAAM,KAAK,UACtB;4BACA,OAAO;gCACL,GAAGD,GAAG;gCACNG,MAAMH,GAAG,CAACC,MAAM;4BAClB;wBACF;oBACF;oBACA,OAAOD;gBACT;gBACA,2GAA2G;gBAC3G,qCAAqC;gBACrC5D;gBACAgE,iBAAgBC,YAAY,EAAEC,OAAO;oBACnC,OAAO3C,QAAQ4C,UAAU,CAACD,YAAY1D,KAAK4D,KAAK,CAAC1E,aAAE,CAACuE,YAAY,CAACC,SAAS;gBAC5E;gBACAG,oBAAoB1C;gBAEpB2C,YACE,mDAAmD;gBACnD3C,WACIsB,YAEA,CAACW,KAAUW,eAAuBC;oBAChC,IAAIC,eAAeD;oBACnB,IAAIC,YAAY,CAAC,EAAE,KAAK,KAAK;wBAC3BA,eAAe,CAAC,EAAE,EAAEA,cAAc;oBACpC;oBAEA,MAAMC,eAAed,IAAIe,OAAO;oBAChC,IAAID,iBAAiBzB,WAAW;wBAC9B,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,wEAAwE;oBACxE,MAAM2B,aAAaF,YAAY,CAACD,aAAa,IAAIC,YAAY,CAACD,eAAe,MAAM;oBACnF,IAAIG,eAAe,OAAO;wBACxB,MAAM,IAAIxF;oBACZ;oBACA,OAAOwF;gBACT;YACR;QACF,EAAE,OAAOC,OAAY;YACnB,IAAIA,iBAAiBzF,iBAAiB;gBACpC,OAAO;oBACLoE,MAAM;gBACR;YACF;YAEA,IAAI,UAAUqB,SAASA,MAAM9E,IAAI,KAAK,oBAAoB;gBACxD,IAAI+E,IAAAA,yBAAc,EAACtD,aAAa;oBAC9B,sDAAsD;oBACtD,OAAO;wBACLgC,MAAM;oBACR;gBACF;gBAEAnE,MAAM;oBAAEmC;oBAAY/B;oBAAUwC;oBAAYN;oBAAU3B;gBAAiB,GAAGuB;gBAExE,MAAM,IAAIvC,yBACR,sFACE,CAAC,EAAE,EAAE+F,IAAAA,0CAAoB,EACvB;oBACEvB,MAAM;oBACNwB,gBAAgBxD;oBAChByD,eAAepD;gBACjB,GACA,MACA,aAAa,EAAEN,QAAQsB,gBAAgB,CAAC,EAAE,CAAC;YAEnD;YACA,MAAMgC;QACR;QAEA,IAAItD,QAAQO,UAAU,CAACoD,IAAI,CAAC,CAACnE,MAAQiB,GAAGmD,QAAQ,CAACpE,OAAO;YACtD,OAAO;gBACLyC,MAAM;gBACN4B,UAAUpD;YACZ;QACF;QAEA,IAAI8C,IAAAA,yBAAc,EAAC9C,KAAK;YACtB,IAAIL,UAAU;gBACZ,OAAO;oBACL6B,MAAM;oBACN4B,UAAUpD;gBACZ;YACF;YACA,wDAAwD;YACxD,6CAA6C;YAC7C,OAAO;gBACLwB,MAAM;YACR;QACF;QAEA,4DAA4D;QAE5D,IAAI/D,aAAa,OAAO;YACtB,+EAA+E;YAC/E,2EAA2E;YAC3E,OAAO;gBACL+D,MAAM;gBACN6B,WAAW;oBAACrD;iBAAG;YACjB;QACF;QAEA,MAAMsD,UAAU3C,eAAI,CAACC,OAAO,CAACZ;QAC7B,MAAMuD,YAAY5C,eAAI,CAAC6C,OAAO,CAACxD;QAC/B,MAAMyD,WAAW9C,eAAI,CAAC8C,QAAQ,CAACzD,IAAIuD;QACnC,OAAO;YACL/B,MAAM;YACN,+CAA+C;YAC/C6B,WAAW9D,QAAQmE,YAAY,CAACJ,SAASG,UAAUF,cAAc;gBAACvD;aAAG;QACvE;IACF;IAEA,OAAOV;AACT"}