var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.Button=void 0;Object.defineProperty(exports,"FA5Style",{enumerable:true,get:function get(){return _createIconSetFromFontawesome.FA5Style;}});exports.getImageSourceSync=exports.getImageSource=exports.default=void 0;var _createIconSetFromFontawesome=require("./lib/create-icon-set-from-fontawesome5");var _FontAwesome5Free=_interopRequireDefault(require("./glyphmaps/FontAwesome5Free.json"));var _FontAwesome5Free_meta=_interopRequireDefault(require("./glyphmaps/FontAwesome5Free_meta.json"));var iconSet=(0,_createIconSetFromFontawesome.createFA5iconSet)(_FontAwesome5Free.default,_FontAwesome5Free_meta.default,false);var _default=exports.default=iconSet;var Button=exports.Button=iconSet.Button,getImageSource=exports.getImageSource=iconSet.getImageSource,getImageSourceSync=exports.getImageSourceSync=iconSet.getImageSourceSync;