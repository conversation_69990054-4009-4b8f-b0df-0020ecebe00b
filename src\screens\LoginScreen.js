import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SIZES } from '../constants/theme';
import { useSecurity } from '../contexts/SecurityContext';

const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);

  const {
    login,
    loginWithBiometric,
    isLoading,
    error,
    clearError,
    biometricAvailable,
    canUseBiometric,
    deviceSecure,
    securityLevel,
    securityScore
  } = useSecurity();

  useEffect(() => {
    // Clear any previous errors when component mounts
    clearError();
  }, []);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    const result = await login(email, password, {
      enableBiometric: biometricAvailable
    });

    if (result.success) {
      Alert.alert(
        'Welcome to Instagram +!',
        'Login successful! Your account is now secured with advanced protection.',
        [
          {
            text: 'Continue',
            onPress: () => {
              // Navigation will be handled by the security context
              console.log('Login successful');
            }
          }
        ]
      );
    } else {
      setLoginAttempts(prev => prev + 1);

      if (loginAttempts >= 4) {
        Alert.alert(
          'Security Alert',
          'Multiple failed login attempts detected. Please wait before trying again or use biometric authentication if available.',
          [
            { text: 'OK' },
            ...(canUseBiometric ? [{
              text: 'Use Biometric',
              onPress: handleBiometricLogin
            }] : [])
          ]
        );
      } else {
        Alert.alert('Login Failed', result.error || 'Invalid credentials');
      }
    }
  };

  const handleBiometricLogin = async () => {
    if (!canUseBiometric) {
      Alert.alert(
        'Biometric Unavailable',
        'Biometric authentication is not available on this device or not set up.'
      );
      return;
    }

    const result = await loginWithBiometric();

    if (result.success) {
      Alert.alert(
        'Secure Login Successful!',
        'You have been authenticated using biometric security.',
        [{ text: 'Continue' }]
      );
    } else {
      Alert.alert('Biometric Login Failed', result.error || 'Authentication failed');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Instagram +</Text>
            <Text style={styles.subtitle}>Instagram with free verification & advanced security</Text>

            {/* Security Status Indicator */}
            <View style={styles.securityIndicator}>
              <View style={[styles.securityBadge, { backgroundColor: getSecurityColor() }]}>
                <Ionicons
                  name={getSecurityIcon()}
                  size={16}
                  color={COLORS.white}
                />
                <Text style={styles.securityText}>
                  {securityLevel.toUpperCase()} SECURITY ({securityScore}%)
                </Text>
              </View>
              {!deviceSecure && (
                <Text style={styles.securityWarning}>
                  ⚠️ Device security compromised
                </Text>
              )}
            </View>
          </View>

          {/* Login Form */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color={COLORS.gray} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                placeholderTextColor={COLORS.gray}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={20} color={COLORS.gray} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor={COLORS.gray}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                <Ionicons
                  name={showPassword ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color={COLORS.gray}
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, isLoading && styles.disabledButton]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={COLORS.white} />
                  <Text style={[styles.loginButtonText, { marginLeft: 8 }]}>
                    Securing Connection...
                  </Text>
                </View>
              ) : (
                <Text style={styles.loginButtonText}>
                  🔐 Secure Sign In
                </Text>
              )}
            </TouchableOpacity>

            {/* Biometric Login Button */}
            {canUseBiometric && (
              <TouchableOpacity
                style={styles.biometricButton}
                onPress={handleBiometricLogin}
                disabled={isLoading}
              >
                <Ionicons name="finger-print" size={20} color={COLORS.primary} />
                <Text style={styles.biometricButtonText}>
                  Use Biometric Authentication
                </Text>
              </TouchableOpacity>
            )}

            {/* Error Display */}
            {error && (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={20} color={COLORS.error} />
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity onPress={clearError}>
                  <Ionicons name="close" size={20} color={COLORS.error} />
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Divider */}
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* Social Login */}
          <View style={styles.socialLogin}>
            <TouchableOpacity style={styles.socialButton}>
              <Ionicons name="logo-google" size={20} color={COLORS.error} />
              <Text style={styles.socialButtonText}>Continue with Google</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.socialButton}>
              <Ionicons name="logo-facebook" size={20} color="#1877F2" />
              <Text style={styles.socialButtonText}>Continue with Facebook</Text>
            </TouchableOpacity>
          </View>

          {/* Verification Badge Info */}
          <View style={styles.verificationInfo}>
            <Ionicons name="checkmark-circle" size={20} color={COLORS.verificationGreen} />
            <Text style={styles.verificationText}>
              Get your free verification badge when you join Instagram +!
            </Text>
          </View>

          {/* Sign Up Link */}
          <View style={styles.signUpContainer}>
            <Text style={styles.signUpText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.signUpLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );

  // Helper functions
  function getSecurityColor() {
    switch (securityLevel) {
      case 'maximum': return COLORS.success;
      case 'high': return COLORS.primary;
      case 'medium': return COLORS.warning;
      default: return COLORS.error;
    }
  }

  function getSecurityIcon() {
    switch (securityLevel) {
      case 'maximum': return 'shield-checkmark';
      case 'high': return 'shield';
      case 'medium': return 'shield-half';
      default: return 'warning';
    }
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  form: {
    marginBottom: 24,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    backgroundColor: COLORS.background,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: COLORS.textPrimary,
    marginLeft: 12,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
  },
  loginButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.border,
  },
  dividerText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginHorizontal: 16,
  },
  socialLogin: {
    marginBottom: 24,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 12,
    paddingVertical: 12,
    marginBottom: 12,
    backgroundColor: COLORS.white,
  },
  socialButtonText: {
    fontSize: 16,
    color: COLORS.textPrimary,
    marginLeft: 12,
    fontWeight: '500',
  },
  verificationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 24,
  },
  verificationText: {
    fontSize: 14,
    color: COLORS.verificationGreen,
    marginLeft: 8,
    fontWeight: '500',
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  signUpLink: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  securityIndicator: {
    alignItems: 'center',
    marginTop: 16,
  },
  securityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
  },
  securityText: {
    fontSize: 12,
    color: COLORS.white,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  securityWarning: {
    fontSize: 12,
    color: COLORS.error,
    textAlign: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 12,
    paddingVertical: 12,
    marginTop: 12,
    backgroundColor: COLORS.white,
  },
  biometricButtonText: {
    fontSize: 16,
    color: COLORS.primary,
    marginLeft: 8,
    fontWeight: '500',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.error,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16,
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    color: COLORS.error,
    marginLeft: 8,
    marginRight: 8,
  },
});

export default LoginScreen;
