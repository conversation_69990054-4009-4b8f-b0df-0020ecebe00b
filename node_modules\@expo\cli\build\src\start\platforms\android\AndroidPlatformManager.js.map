{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidPlatformManager.ts"], "sourcesContent": ["import { AndroidAppIdResolver } from './AndroidAppIdResolver';\nimport { AndroidDeviceManager } from './AndroidDeviceManager';\nimport { Device } from './adb';\nimport { startAdbReverseAsync } from './adbReverse';\nimport { CommandError } from '../../../utils/errors';\nimport { memoize } from '../../../utils/fn';\nimport { learnMore } from '../../../utils/link';\nimport { hasDirectDevClientDependency } from '../../detectDevClient';\nimport { AppIdResolver } from '../AppIdResolver';\nimport { BaseOpenInCustomProps, BaseResolveDeviceProps, PlatformManager } from '../PlatformManager';\n\nconst debug = require('debug')(\n  'expo:start:platforms:platformManager:android'\n) as typeof console.log;\n\nexport interface AndroidOpenInCustomProps extends BaseOpenInCustomProps {\n  /**\n   * The Android app intent to launch through `adb shell am start -n <launchActivity>`.\n   */\n  launchActivity?: string;\n  /**\n   * The custom app id to launch, provided through `--app-id`.\n   * By default, the app id is identical to the package name.\n   * When using product flavors, the app id might be customized.\n   */\n  customAppId?: string;\n}\n\nexport class AndroidPlatformManager extends PlatformManager<Device, AndroidOpenInCustomProps> {\n  /** The last used custom launch props, should be reused whenever launching custom runtime without launch props */\n  private lastCustomRuntimeLaunchProps?: AndroidOpenInCustomProps;\n  /** Memoized method to detect if dev client is installed */\n  private hasDevClientInstalled: () => boolean;\n\n  constructor(\n    protected projectRoot: string,\n    protected port: number,\n    options: {\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL */\n      getExpoGoUrl: () => string;\n      /** Get redirect URL for native disambiguation. */\n      getRedirectUrl: () => string | null;\n      /** Dev Client URL. */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n    }\n  ) {\n    super(projectRoot, {\n      platform: 'android',\n      ...options,\n      resolveDeviceAsync: AndroidDeviceManager.resolveAsync,\n    });\n\n    this.hasDevClientInstalled = memoize(hasDirectDevClientDependency.bind(this, projectRoot));\n  }\n\n  async openAsync(\n    options:\n      | { runtime: 'expo' | 'web' }\n      | { runtime: 'custom'; props?: Partial<AndroidOpenInCustomProps> },\n    resolveSettings?: Partial<BaseResolveDeviceProps<Device>>\n  ): Promise<{ url: string }> {\n    await startAdbReverseAsync([this.port]);\n\n    if (options.runtime === 'custom') {\n      // Store the resolved launch properties for future \"openAsync\" request.\n      // This reuses the same launch properties when opening through the CLI interface (pressing `a`).\n      if (options.props) {\n        this.lastCustomRuntimeLaunchProps = options.props;\n      } else if (!options.props && this.lastCustomRuntimeLaunchProps) {\n        options.props = this.lastCustomRuntimeLaunchProps;\n      }\n\n      // Handle projects that need to launch with a custom app id and launch activity\n      return this.openProjectInCustomRuntimeWithCustomAppIdAsync(options, resolveSettings);\n    }\n\n    return super.openAsync(options, resolveSettings);\n  }\n\n  /**\n   * Launch the custom runtime project, using the provided custom app id and launch activity.\n   * Instead of \"open url\", this will launch the activity directly.\n   * If dev client is installed, it will also pass the dev client URL to the activity.\n   */\n  async openProjectInCustomRuntimeWithCustomAppIdAsync(\n    options: { runtime: 'custom'; props?: Partial<AndroidOpenInCustomProps> },\n    resolveSettings?: Partial<BaseResolveDeviceProps<Device>>\n  ) {\n    // Fall back to default dev client URL open behavior if no custom app id or launch activity is provided\n    if (!options.props?.customAppId || !options.props?.launchActivity) {\n      return super.openProjectInCustomRuntimeAsync(resolveSettings, options.props);\n    }\n\n    const { customAppId, launchActivity } = options.props;\n    const url = this.hasDevClientInstalled()\n      ? (this.props.getCustomRuntimeUrl({ scheme: options.props.scheme }) ?? undefined)\n      : undefined;\n\n    debug(`Opening custom runtime using launch activity: ${launchActivity} --`, options.props);\n\n    const deviceManager = (await this.props.resolveDeviceAsync(\n      resolveSettings\n    )) as AndroidDeviceManager;\n\n    if (!(await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(customAppId))) {\n      throw new CommandError(\n        `No development build (${customAppId}) for this project is installed. ` +\n          `Install a development build on the target device and try again.\\n${learnMore(\n            'https://docs.expo.dev/development/build/'\n          )}`\n      );\n    }\n\n    deviceManager.logOpeningUrl(url ?? launchActivity);\n    await deviceManager.activateWindowAsync();\n    await deviceManager.launchActivityAsync(launchActivity, url);\n\n    return { url: url ?? launchActivity };\n  }\n\n  _getAppIdResolver(): AppIdResolver {\n    return new AndroidAppIdResolver(this.projectRoot);\n  }\n\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props?: Partial<AndroidOpenInCustomProps>\n  ): string {\n    return props?.launchActivity ?? `${applicationId}/.MainActivity`;\n  }\n}\n"], "names": ["AndroidPlatformManager", "debug", "require", "PlatformManager", "constructor", "projectRoot", "port", "options", "platform", "resolveDeviceAsync", "AndroidDeviceManager", "resolveAsync", "hasDevClientInstalled", "memoize", "hasDirectDevClientDependency", "bind", "openAsync", "resolveSettings", "startAdbReverseAsync", "runtime", "props", "lastCustomRuntimeLaunchProps", "openProjectInCustomRuntimeWithCustomAppIdAsync", "customAppId", "launchActivity", "openProjectInCustomRuntimeAsync", "url", "getCustomRuntimeUrl", "scheme", "undefined", "deviceManager", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "CommandError", "learnMore", "logOpeningUrl", "activateWindowAsync", "launchActivityAsync", "_getAppIdResolver", "AndroidAppIdResolver", "_resolveAlternativeLaunchUrl", "applicationId"], "mappings": ";;;;+BA4BaA;;;eAAAA;;;sCA5BwB;sCACA;4BAEA;wBACR;oBACL;sBACE;iCACmB;iCAEkC;AAE/E,MAAMC,QAAQC,QAAQ,SACpB;AAgBK,MAAMF,+BAA+BG,gCAAe;IAMzDC,YACE,AAAUC,WAAmB,EAC7B,AAAUC,IAAY,EACtBC,OASC,CACD;QACA,KAAK,CAACF,aAAa;YACjBG,UAAU;YACV,GAAGD,OAAO;YACVE,oBAAoBC,0CAAoB,CAACC,YAAY;QACvD,SAjBUN,cAAAA,kBACAC,OAAAA;QAkBV,IAAI,CAACM,qBAAqB,GAAGC,IAAAA,WAAO,EAACC,6CAA4B,CAACC,IAAI,CAAC,IAAI,EAAEV;IAC/E;IAEA,MAAMW,UACJT,OAEoE,EACpEU,eAAyD,EAC/B;QAC1B,MAAMC,IAAAA,gCAAoB,EAAC;YAAC,IAAI,CAACZ,IAAI;SAAC;QAEtC,IAAIC,QAAQY,OAAO,KAAK,UAAU;YAChC,uEAAuE;YACvE,gGAAgG;YAChG,IAAIZ,QAAQa,KAAK,EAAE;gBACjB,IAAI,CAACC,4BAA4B,GAAGd,QAAQa,KAAK;YACnD,OAAO,IAAI,CAACb,QAAQa,KAAK,IAAI,IAAI,CAACC,4BAA4B,EAAE;gBAC9Dd,QAAQa,KAAK,GAAG,IAAI,CAACC,4BAA4B;YACnD;YAEA,+EAA+E;YAC/E,OAAO,IAAI,CAACC,8CAA8C,CAACf,SAASU;QACtE;QAEA,OAAO,KAAK,CAACD,UAAUT,SAASU;IAClC;IAEA;;;;GAIC,GACD,MAAMK,+CACJf,OAAyE,EACzEU,eAAyD,EACzD;YAEKV,gBAA+BA;QADpC,uGAAuG;QACvG,IAAI,GAACA,iBAAAA,QAAQa,KAAK,qBAAbb,eAAegB,WAAW,KAAI,GAAChB,kBAAAA,QAAQa,KAAK,qBAAbb,gBAAeiB,cAAc,GAAE;YACjE,OAAO,KAAK,CAACC,gCAAgCR,iBAAiBV,QAAQa,KAAK;QAC7E;QAEA,MAAM,EAAEG,WAAW,EAAEC,cAAc,EAAE,GAAGjB,QAAQa,KAAK;QACrD,MAAMM,MAAM,IAAI,CAACd,qBAAqB,KACjC,IAAI,CAACQ,KAAK,CAACO,mBAAmB,CAAC;YAAEC,QAAQrB,QAAQa,KAAK,CAACQ,MAAM;QAAC,MAAMC,YACrEA;QAEJ5B,MAAM,CAAC,8CAA8C,EAAEuB,eAAe,GAAG,CAAC,EAAEjB,QAAQa,KAAK;QAEzF,MAAMU,gBAAiB,MAAM,IAAI,CAACV,KAAK,CAACX,kBAAkB,CACxDQ;QAGF,IAAI,CAAE,MAAMa,cAAcC,mDAAmD,CAACR,cAAe;YAC3F,MAAM,IAAIS,oBAAY,CACpB,CAAC,sBAAsB,EAAET,YAAY,iCAAiC,CAAC,GACrE,CAAC,iEAAiE,EAAEU,IAAAA,eAAS,EAC3E,6CACC;QAET;QAEAH,cAAcI,aAAa,CAACR,OAAOF;QACnC,MAAMM,cAAcK,mBAAmB;QACvC,MAAML,cAAcM,mBAAmB,CAACZ,gBAAgBE;QAExD,OAAO;YAAEA,KAAKA,OAAOF;QAAe;IACtC;IAEAa,oBAAmC;QACjC,OAAO,IAAIC,0CAAoB,CAAC,IAAI,CAACjC,WAAW;IAClD;IAEAkC,6BACEC,aAAqB,EACrBpB,KAAyC,EACjC;QACR,OAAOA,CAAAA,yBAAAA,MAAOI,cAAc,KAAI,GAAGgB,cAAc,cAAc,CAAC;IAClE;AACF"}