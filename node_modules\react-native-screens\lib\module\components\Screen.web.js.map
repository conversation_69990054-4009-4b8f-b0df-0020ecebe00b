{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "Animated", "View", "React", "screensEnabled", "InnerScreen", "NativeScreen", "Component", "render", "active", "activityState", "style", "enabled", "rest", "props", "undefined", "createElement", "hidden", "display", "Screen", "createAnimatedComponent", "ScreenContext", "createContext"], "sourceRoot": "../../../src", "sources": ["components/Screen.web.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAGb,SAASO,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,QAAQ,SAAS;AAExC,OAAO,MAAMC,WAAW,GAAGH,IAAI;;AAE/B;AACA;AACA;AACA,OAAO,MAAMI,YAAY,SAASH,KAAK,CAACI,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAGR,cAAc,CAAC,CAAC;MAC1B,GAAGS;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIF,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKM,SAAS,IAAIL,aAAa,KAAKK,SAAS,EAAE;QACvDL,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;MACA,oBACEN,KAAA,CAAAa,aAAA,CAACd;MACC;MAAA,EAAAd,QAAA;QACA6B,MAAM,EAAEP,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEO,OAAO,EAAER,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DG,IAAI,CACT,CAAC;IAEN;IAEA,oBAAOV,KAAA,CAAAa,aAAA,CAACd,IAAI,EAAKW,IAAO,CAAC;EAC3B;AACF;AAEA,MAAMM,MAAM,GAAGlB,QAAQ,CAACmB,uBAAuB,CAACd,YAAY,CAAC;AAE7D,OAAO,MAAMe,aAAa,gBAAGlB,KAAK,CAACmB,aAAa,CAACH,MAAM,CAAC;AAExD,eAAeA,MAAM", "ignoreList": []}