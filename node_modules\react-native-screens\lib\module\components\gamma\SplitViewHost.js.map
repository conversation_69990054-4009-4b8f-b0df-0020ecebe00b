{"version": 3, "names": ["React", "StyleSheet", "SplitViewHostNativeComponent", "displayModeForSplitViewCompatibilityMap", "tile", "overlay", "displace", "automatic", "isValidDisplayModeForSplitBehavior", "displayMode", "splitBehavior", "includes", "SplitViewHost", "props", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "validDisplayModes", "console", "warn", "join", "createElement", "_extends", "style", "styles", "container", "children", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["components/gamma/SplitViewHost.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,cAAc;AAEzC,OAAOC,4BAA4B,MAAM,iDAAiD;AAe1F;AACA;AACA;AACA;AACA,MAAMC,uCAGL,GAAG;EACFC,IAAI,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,oBAAoB,CAAC;EACnEC,OAAO,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;EAClEC,QAAQ,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,sBAAsB,CAAC;EACzEC,SAAS,EAAE,EAAE,CAAE;AACjB,CAAC;AAED,MAAMC,kCAAkC,GAAGA,CACzCC,WAAiC,EACjCC,aAAqC,KAClC;EACH,IAAIA,aAAa,KAAK,WAAW,EAAE;IACjC;IACA,OAAO,IAAI;EACb;EACA,OAAOP,uCAAuC,CAACO,aAAa,CAAC,CAACC,QAAQ,CACpEF,WACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,SAASG,aAAaA,CAACC,KAAyB,EAAE;EAChD,MAAM;IAAEJ,WAAW;IAAEC;EAAc,CAAC,GAAGG,KAAK;EAE5Cb,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAIL,WAAW,IAAIC,aAAa,EAAE;MAChC,MAAMK,OAAO,GAAGP,kCAAkC,CAChDC,WAAW,EACXC,aACF,CAAC;MACD,IAAI,CAACK,OAAO,EAAE;QACZ,MAAMC,iBAAiB,GACrBb,uCAAuC,CAACO,aAAa,CAAC;QACxDO,OAAO,CAACC,IAAI,CACV,yBAAyBT,WAAW,yBAAyBC,aAAa,IAAI,GAC5E,sBAAsBA,aAAa,UAAUM,iBAAiB,CAACG,IAAI,CACjE,IACF,CAAC,GACL,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACV,WAAW,EAAEC,aAAa,CAAC,CAAC;EAEhC,oBACEV,KAAA,CAAAoB,aAAA,CAAClB,4BAA4B,EAAAmB,QAAA,KAAKR,KAAK;IAAES,KAAK,EAAEC,MAAM,CAACC;EAAU,IAC9DX,KAAK,CAACY,QACqB,CAAC;AAEnC;AAEA,MAAMF,MAAM,GAAGtB,UAAU,CAACyB,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAef,aAAa", "ignoreList": []}