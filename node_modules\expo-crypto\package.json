{"name": "expo-crypto", "version": "14.1.5", "description": "Provides cryptography primitives for Android, iOS and web.", "main": "build/Crypto.js", "types": "build/Crypto.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "react-native-web", "expo", "crypto", "ios", "android", "web", "native"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-crypto"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/crypto/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"base64-js": "^1.3.0"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "cf75dcd9ba3da338ad778b15a45e15fca9508cc5"}