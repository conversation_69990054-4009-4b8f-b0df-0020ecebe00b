'use client';

import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';

// @ts-ignore: ImageSource type has been recently added: https://github.com/facebook/react-native/pull/51969

// iOS-specific: SFSymbol, image as a template usage

// eslint-disable-next-line @typescript-eslint/ban-types

export default codegenNativeComponent('RNSBottomTabsScreen', {});
//# sourceMappingURL=BottomTabsScreenNativeComponent.js.map