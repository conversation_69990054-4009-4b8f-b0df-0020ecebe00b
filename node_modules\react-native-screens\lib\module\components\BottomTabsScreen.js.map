{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "BottomTabsScreenNativeComponent", "StyleSheet", "findNodeHandle", "Freeze", "freezeEnabled", "featureFlags", "BottomTabsScreen", "props", "componentNodeRef", "useRef", "componentNodeHandle", "useEffect", "current", "nativeViewIsVisible", "setNativeViewIsVisible", "useState", "onWillAppear", "onDidAppear", "onWillDisappear", "onDidDisappear", "isFocused", "icon", "selectedIcon", "rest", "shouldFreeze", "experiment", "controlledBottomTabs", "onWillAppearCallback", "useCallback", "event", "console", "log", "onDidAppearCallback", "onWillDisappearCallback", "onDidDisappearCallback", "info", "tabKey", "iconProps", "parseIconsToNativeProps", "createElement", "collapsable", "style", "styles", "fillParent", "ref", "freeze", "placeholder", "children", "parseIconToNativeProps", "iconType", "iconSfSymbolName", "sfSymbolName", "iconImageSource", "imageSource", "templateSource", "Error", "selectedIconImageSource", "selectedIconSfSymbolName", "selectedIconType", "undefined", "create", "position", "flex", "width", "height"], "sourceRoot": "../../../src", "sources": ["components/BottomTabsScreen.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AACzB,OAAOC,+BAA+B,MAI/B,2CAA2C;AAClD,SAIEC,UAAU,EAGVC,cAAc,QACT,cAAc;AACrB,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,YAAY,QAAQ,UAAU;;AAOvC;;AASA;;AAKA;;AA2DA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAA4B,EAAE;EACtD,MAAMC,gBAAgB,GAAGT,KAAK,CAACU,MAAM,CAA+B,IAAI,CAAC;EACzE,MAAMC,mBAAmB,GAAGX,KAAK,CAACU,MAAM,CAAS,CAAC,CAAC,CAAC;EAEpDV,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIH,gBAAgB,CAACI,OAAO,IAAI,IAAI,EAAE;MACpCF,mBAAmB,CAACE,OAAO,GACzBV,cAAc,CAACM,gBAAgB,CAACI,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,MAAM;MACLF,mBAAmB,CAACE,OAAO,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAM;IACJC,YAAY;IACZC,WAAW;IACXC,eAAe;IACfC,cAAc;IACdC,SAAS,GAAG,KAAK;IACjBC,IAAI;IACJC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGhB,KAAK;EAET,IAAIiB,YAAY,GAAGpB,aAAa,CAAC,CAAC;EAElC,IAAIC,YAAY,CAACoB,UAAU,CAACC,oBAAoB,EAAE;IAChD;IACAF,YAAY,GAAGA,YAAY,IAAI,CAACX,mBAAmB,IAAI,CAACO,SAAS;EACnE,CAAC,MAAM;IACLI,YAAY,GAAGA,YAAY,IAAI,CAACX,mBAAmB;EACrD;EAEA,MAAMc,oBAAoB,GAAG5B,KAAK,CAAC6B,WAAW,CAC3CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAerB,mBAAmB,CAACE,OAAO,yBAC5C,CAAC;IACDE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,YAAY,GAAGa,KAAK,CAAC;EACvB,CAAC,EACD,CAACb,YAAY,CACf,CAAC;EAED,MAAMgB,mBAAmB,GAAGjC,KAAK,CAAC6B,WAAW,CAC1CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAerB,mBAAmB,CAACE,OAAO,wBAC5C,CAAC;IACDK,WAAW,GAAGY,KAAK,CAAC;EACtB,CAAC,EACD,CAACZ,WAAW,CACd,CAAC;EAED,MAAMgB,uBAAuB,GAAGlC,KAAK,CAAC6B,WAAW,CAC9CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAerB,mBAAmB,CAACE,OAAO,4BAC5C,CAAC;IACDM,eAAe,GAAGW,KAAK,CAAC;EAC1B,CAAC,EACD,CAACX,eAAe,CAClB,CAAC;EAED,MAAMgB,sBAAsB,GAAGnC,KAAK,CAAC6B,WAAW,CAC7CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAerB,mBAAmB,CAACE,OAAO,2BAC5C,CAAC;IACDE,sBAAsB,CAAC,KAAK,CAAC;IAC7BK,cAAc,GAAGU,KAAK,CAAC;EACzB,CAAC,EACD,CAACV,cAAc,CACjB,CAAC;EAEDW,OAAO,CAACK,IAAI,CACV,eAAezB,mBAAmB,CAACE,OAAO,IAAI,CAAC,CAAC,qBAC9CW,IAAI,CAACa,MAAM,kBACKZ,YAAY,gBAAgBJ,SAAS,yBAAyBP,mBAAmB,EACrG,CAAC;EAED,MAAMwB,SAAS,GAAGC,uBAAuB,CAACjB,IAAI,EAAEC,YAAY,CAAC;EAE7D,oBACEvB,KAAA,CAAAwC,aAAA,CAACvC,+BAA+B,EAAAd,QAAA;IAC9BsD,WAAW,EAAE,KAAM;IACnBC,KAAK,EAAEC,MAAM,CAACC,UAAW;IACzB3B,YAAY,EAAEW,oBAAqB;IACnCV,WAAW,EAAEe,mBAAoB;IACjCd,eAAe,EAAEe,uBAAwB;IACzCd,cAAc,EAAEe,sBAAuB;IACvCd,SAAS,EAAEA;EAAU,GACjBiB,SAAS;IACb;IACAO,GAAG,EAAEpC;EAAiB,GAClBe,IAAI,gBACRxB,KAAA,CAAAwC,aAAA,CAACpC,MAAM;IAAC0C,MAAM,EAAErB,YAAa;IAACsB,WAAW,EAAEvB,IAAI,CAACuB;EAAY,GACzDvB,IAAI,CAACwB,QACA,CACuB,CAAC;AAEtC;AAEA,SAASC,sBAAsBA,CAAC3B,IAAsB,EAIpD;EACA,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,CAAC,CAAC;EACX;EAEA,IAAI,cAAc,IAAIA,IAAI,EAAE;IAC1B;IACA,OAAO;MACL4B,QAAQ,EAAE,UAAU;MACpBC,gBAAgB,EAAE7B,IAAI,CAAC8B;IACzB,CAAC;EACH,CAAC,MAAM,IAAI,aAAa,IAAI9B,IAAI,EAAE;IAChC,OAAO;MACL4B,QAAQ,EAAE,OAAO;MACjBG,eAAe,EAAE/B,IAAI,CAACgC;IACxB,CAAC;EACH,CAAC,MAAM,IAAI,gBAAgB,IAAIhC,IAAI,EAAE;IACnC;IACA,OAAO;MACL4B,QAAQ,EAAE,UAAU;MACpBG,eAAe,EAAE/B,IAAI,CAACiC;IACxB,CAAC;EACH,CAAC,MAAM;IACL;IACA,MAAM,IAAIC,KAAK,CACb,kGACF,CAAC;EACH;AACF;AAEA,SAASjB,uBAAuBA,CAC9BjB,IAAsB,EACtBC,YAA8B,EAO9B;EACA,MAAM;IAAE8B,eAAe;IAAEF,gBAAgB;IAAED;EAAS,CAAC,GACnDD,sBAAsB,CAAC3B,IAAI,CAAC;EAC9B,MAAM;IACJ+B,eAAe,EAAEI,uBAAuB;IACxCN,gBAAgB,EAAEO,wBAAwB;IAC1CR,QAAQ,EAAES;EACZ,CAAC,GAAGV,sBAAsB,CAAC1B,YAAY,CAAC;EAExC,IACE2B,QAAQ,KAAKU,SAAS,IACtBD,gBAAgB,KAAKC,SAAS,IAC9BV,QAAQ,KAAKS,gBAAgB,EAC7B;IACA,MAAM,IAAIH,KAAK,CAAC,sDAAsD,CAAC;EACzE,CAAC,MAAM,IAAIN,QAAQ,KAAKU,SAAS,IAAID,gBAAgB,KAAKC,SAAS,EAAE;IACnE;IACA,MAAM,IAAIJ,KAAK,CACb,4EACF,CAAC;EACH;EAEA,OAAO;IACLN,QAAQ;IACRG,eAAe;IACfF,gBAAgB;IAChBM,uBAAuB;IACvBC;EACF,CAAC;AACH;AAEA,eAAenD,gBAAgB;AAE/B,MAAMoC,MAAM,GAAGzC,UAAU,CAAC2D,MAAM,CAAC;EAC/BjB,UAAU,EAAE;IACVkB,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}