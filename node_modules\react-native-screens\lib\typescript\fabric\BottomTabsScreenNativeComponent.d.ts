/// <reference types="react-native/types/modules/Codegen" />
import type { ColorValue, ViewProps } from 'react-native';
import { DirectEventHandler, Float, Int32, WithDefault } from 'react-native/Libraries/Types/CodegenTypes';
import type { ImageSource } from 'react-native/Libraries/Image/ImageSource';
export type IconType = 'image' | 'template' | 'sfSymbol';
export type GenericEmptyEvent = Readonly<{}>;
export type LifecycleStateChangeEvent = Readonly<{
    previousState: Int32;
    newState: Int32;
}>;
export type BlurEffect = 'none' | 'extraLight' | 'light' | 'dark' | 'regular' | 'prominent' | 'systemUltraThinMaterial' | 'systemThinMaterial' | 'systemMaterial' | 'systemThickMaterial' | 'systemChromeMaterial' | 'systemUltraThinMaterialLight' | 'systemThinMaterialLight' | 'systemMaterialLight' | 'systemThickMaterialLight' | 'systemChromeMaterialLight' | 'systemUltraThinMaterialDark' | 'systemThinMaterialDark' | 'systemMaterialDark' | 'systemThickMaterialDark' | 'systemChromeMaterialDark';
export interface NativeProps extends ViewProps {
    onLifecycleStateChange?: DirectEventHandler<LifecycleStateChangeEvent>;
    onWillAppear?: DirectEventHandler<GenericEmptyEvent>;
    onDidAppear?: DirectEventHandler<GenericEmptyEvent>;
    onWillDisappear?: DirectEventHandler<GenericEmptyEvent>;
    onDidDisappear?: DirectEventHandler<GenericEmptyEvent>;
    isFocused?: boolean;
    tabKey: string;
    tabBarBackgroundColor?: ColorValue;
    tabBarBlurEffect?: WithDefault<BlurEffect, 'none'>;
    tabBarItemTitleFontFamily?: string;
    tabBarItemTitleFontSize?: Float;
    tabBarItemTitleFontWeight?: string;
    tabBarItemTitleFontStyle?: string;
    tabBarItemTitleFontColor?: ColorValue;
    tabBarItemTitlePositionAdjustment?: {
        horizontal?: Float;
        vertical?: Float;
    };
    tabBarItemIconColor?: ColorValue;
    tabBarItemBadgeBackgroundColor?: ColorValue;
    title?: string | undefined | null;
    iconResourceName?: string;
    iconType?: WithDefault<IconType, 'sfSymbol'>;
    iconImageSource?: ImageSource;
    iconSfSymbolName?: string;
    selectedIconImageSource?: ImageSource;
    selectedIconSfSymbolName?: string;
    badgeValue?: string;
    specialEffects?: {
        repeatedTabSelection?: {
            popToRoot?: WithDefault<boolean, true>;
            scrollToTop?: WithDefault<boolean, true>;
        };
    };
    overrideScrollViewContentInsetAdjustmentBehavior?: WithDefault<boolean, true>;
}
declare const _default: import("react-native/Libraries/Utilities/codegenNativeComponent").NativeComponentType<NativeProps>;
export default _default;
//# sourceMappingURL=BottomTabsScreenNativeComponent.d.ts.map