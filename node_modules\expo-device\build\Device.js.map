{"version": 3, "file": "Device.js", "sourceRoot": "", "sources": ["../src/Device.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,UAAU,MAAM,cAAc,CAAC;AAEtC,OAAO,EAAE,UAAU,EAAE,CAAC;AAEtB;;;GAGG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAY,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;AAEzE;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,KAAK,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AAEzE;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,YAAY,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;AAEvF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAEtE;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,SAAS,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;AAEjF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,UAAU,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAE3F;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,WAAW,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAE7F;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,UAAU,GAAsB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;AAE7F;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,WAAW,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;AAErF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAoB,UAAU;IAClE,CAAC,CAAC,UAAU,CAAC,yBAAyB;IACtC,CAAC,CAAC,IAAI,CAAC;AAET;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,MAAM,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;AAE3E;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,SAAS,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;AAEjF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,SAAS,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;AAEjF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC;AAEjG;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAkB,UAAU;IACzD,CAAC,CAAC,UAAU,CAAC,kBAAkB,IAAI,IAAI;IACvC,CAAC,CAAC,IAAI,CAAC;AAET;;;;;;;;;;GAUG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAkB,UAAU;IACvD,CAAC,CAAC,UAAU,CAAC,gBAAgB,IAAI,IAAI;IACrC,CAAC,CAAC,IAAI,CAAC;AAET;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,UAAU,GAAkB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAEnF;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB;IACtC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;QACnC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,kBAAkB,EAAE,CAAC;AAC/C,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc;IAClC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;QAC/B,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;AAC3C,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB;IACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;IACpE,CAAC;IACD,IAAI,SAAS,GAAG,MAAM,UAAU,CAAC,iBAAiB,EAAE,CAAC;IACrD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;QACrB,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACtC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC;QAC1C,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,yBAAyB,EAAE,CAAC;AACtD,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC;QAC1C,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,yBAAyB,EAAE,CAAC;AACtD,CAAC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB;IAC5C,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC;QACzC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,wBAAwB,EAAE,CAAC;AACrD,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,OAAe;IAC3D,IAAI,CAAC,UAAU,CAAC,uBAAuB,EAAE,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,MAAM,UAAU,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC3D,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport { DeviceType } from './Device.types';\nimport ExpoDevice from './ExpoDevice';\n\nexport { DeviceType };\n\n/**\n * `true` if the app is running on a real device and `false` if running in a simulator or emulator.\n * On web, this is always set to `true`.\n */\nexport const isDevice: boolean = ExpoDevice ? ExpoDevice.isDevice : true;\n\n/**\n * The device brand. The consumer-visible brand of the product/hardware. On web, this value is always `null`.\n *\n * @example\n * ```js\n * Device.brand; // Android: \"google\", \"xiaomi\"; iOS: \"Apple\"; web: null\n * ```\n * @platform android\n * @platform ios\n */\nexport const brand: string | null = ExpoDevice ? ExpoDevice.brand : null;\n\n/**\n * The actual device manufacturer of the product or hardware. This value of this field may be `null` if it cannot be determined.\n *\n * To view difference between `brand` and `manufacturer` on Android see [official documentation](https://developer.android.com/reference/android/os/Build).\n *\n * @example\n * ```js\n * Device.manufacturer; // Android: \"Google\", \"xiaomi\"; iOS: \"Apple\"; web: \"Google\", null\n * ```\n */\nexport const manufacturer: string | null = ExpoDevice ? ExpoDevice.manufacturer : null;\n\n/**\n * The internal model ID of the device. This is useful for programmatically identifying the type of device and is not a human-friendly string.\n * On web and Android, this value is always `null`.\n *\n * @example\n * ```js\n * Device.modelId; // iOS: \"iPhone7,2\"; Android: null; web: null\n * ```\n * @platform ios\n */\nexport const modelId = ExpoDevice ? ExpoDevice.modelId || null : null;\n\n/**\n * The human-friendly name of the device model. This is the name that people would typically use to refer to the device rather than a programmatic model identifier.\n * This value of this field may be `null` if it cannot be determined.\n *\n * @example\n * ```js\n * Device.modelName; // Android: \"Pixel 2\"; iOS: \"iPhone XS Max\"; web: \"iPhone\", null\n * ```\n */\nexport const modelName: string | null = ExpoDevice ? ExpoDevice.modelName : null;\n\n/**\n * The specific configuration or name of the industrial design. It represents the device's name when it was designed during manufacturing into mass production.\n * On Android, it corresponds to [`Build.DEVICE`](https://developer.android.com/reference/android/os/Build#DEVICE). On web and iOS, this value is always `null`.\n *\n * @example\n * ```js\n * Device.designName; // Android: \"kminilte\"; iOS: null; web: null\n * ```\n * @platform android\n */\nexport const designName: string | null = ExpoDevice ? ExpoDevice.designName || null : null;\n\n/**\n * The device's overall product name chosen by the device implementer containing the development name or code name of the device.\n * Corresponds to [`Build.PRODUCT`](https://developer.android.com/reference/android/os/Build#PRODUCT). On web and iOS, this value is always `null`.\n *\n * @example\n * ```js\n * Device.productName; // Android: \"kminiltexx\"; iOS: null; web: null\n * ```\n * @platform android\n */\nexport const productName: string | null = ExpoDevice ? ExpoDevice.productName || null : null;\n\n/**\n * The type of the device as a [`DeviceType`](#devicetype) enum value.\n *\n * On Android, for devices other than TVs, the device type is determined by the screen resolution (screen diagonal size), so the result may not be completely accurate.\n * If the screen diagonal length is between 3\" and 6.9\", the method returns `DeviceType.PHONE`. For lengths between 7\" and 18\", the method returns `DeviceType.TABLET`.\n * Otherwise, the method returns `DeviceType.UNKNOWN`.\n *\n * @example\n * ```js\n * Device.deviceType; // UNKNOWN, PHONE, TABLET, TV, DESKTOP\n * ```\n */\nexport const deviceType: DeviceType | null = ExpoDevice ? ExpoDevice.deviceType : null;\n\n/**\n * The [device year class](https://github.com/facebook/device-year-class) of this device. On web, this value is always `null`.\n */\nexport const deviceYearClass: number | null = ExpoDevice ? ExpoDevice.deviceYearClass : null;\n\n/**\n * The device's total memory, in bytes. This is the total memory accessible to the kernel, but not necessarily to a single app.\n * This is basically the amount of RAM the device has, not including below-kernel fixed allocations like DMA buffers, RAM for the baseband CPU, etc…\n * On web, this value is always `null`.\n *\n * @example\n * ```js\n * Device.totalMemory; // 17179869184\n * ```\n */\nexport const totalMemory: number | null = ExpoDevice ? ExpoDevice.totalMemory : null;\n\n/**\n * A list of supported processor architecture versions. The device expects the binaries it runs to be compiled for one of these architectures.\n * This value is `null` if the supported architectures could not be determined, particularly on web.\n *\n * @example\n * ```js\n * Device.supportedCpuArchitectures; // ['arm64 v8', 'Intel x86-64h Haswell', 'arm64-v8a', 'armeabi-v7a\", 'armeabi']\n * ```\n */\nexport const supportedCpuArchitectures: string[] | null = ExpoDevice\n  ? ExpoDevice.supportedCpuArchitectures\n  : null;\n\n/**\n * The name of the OS running on the device.\n *\n * @example\n * ```js\n * Device.osName; // Android: \"Android\"; iOS: \"iOS\" or \"iPadOS\"; web: \"iOS\", \"Android\", \"Windows\"\n * ```\n */\nexport const osName: string | null = ExpoDevice ? ExpoDevice.osName : null;\n\n/**\n * The human-readable OS version string. Note that the version string may not always contain three numbers separated by dots.\n *\n * @example\n * ```js\n * Device.osVersion; // Android: \"4.0.3\"; iOS: \"12.3.1\"; web: \"11.0\", \"8.1.0\"\n * ```\n */\nexport const osVersion: string | null = ExpoDevice ? ExpoDevice.osVersion : null;\n\n/**\n * The build ID of the OS that more precisely identifies the version of the OS. On Android, this corresponds to `Build.DISPLAY` (not `Build.ID`)\n * and currently is a string as described [here](https://source.android.com/setup/start/build-numbers). On iOS, this corresponds to `kern.osversion`\n * and is the detailed OS version sometimes displayed next to the more human-readable version. On web, this value is always `null`.\n *\n * @example\n * ```js\n * Device.osBuildId; // Android: \"PSR1.180720.075\"; iOS: \"16F203\"; web: null\n * ```\n */\nexport const osBuildId: string | null = ExpoDevice ? ExpoDevice.osBuildId : null;\n\n/**\n * The internal build ID of the OS running on the device. On Android, this corresponds to `Build.ID`.\n * On iOS, this is the same value as [`Device.osBuildId`](#deviceosbuildid). On web, this value is always `null`.\n *\n * @example\n * ```js\n * Device.osInternalBuildId; // Android: \"MMB29K\"; iOS: \"16F203\"; web: null,\n * ```\n */\nexport const osInternalBuildId: string | null = ExpoDevice ? ExpoDevice.osInternalBuildId : null;\n\n/**\n * A string that uniquely identifies the build of the currently running system OS. On Android, it follows this template:\n * - `$(BRAND)/$(PRODUCT)/$(DEVICE)/$(BOARD):$(VERSION.RELEASE)/$(ID)/$(VERSION.INCREMENTAL):$(TYPE)/\\$(TAGS)`\n * On web and iOS, this value is always `null`.\n *\n * @example\n * ```js\n * Device.osBuildFingerprint;\n * // Android: \"google/sdk_gphone_x86/generic_x86:9/PSR1.180720.075/5124027:user/release-keys\";\n * // iOS: null; web: null\n * ```\n * @platform android\n */\nexport const osBuildFingerprint: string | null = ExpoDevice\n  ? ExpoDevice.osBuildFingerprint || null\n  : null;\n\n/**\n * The Android SDK version of the software currently running on this hardware device. This value never changes while a device is booted,\n * but it may increase when the hardware manufacturer provides an OS update. See [here](https://developer.android.com/reference/android/os/Build.VERSION_CODES.html)\n * to see all possible version codes and corresponding versions. On iOS and web, this value is always `null`.\n *\n * @example\n * ```js\n * Device.platformApiLevel; // Android: 19; iOS: null; web: null\n * ```\n * @platform android\n */\nexport const platformApiLevel: number | null = ExpoDevice\n  ? ExpoDevice.platformApiLevel || null\n  : null;\n\n/**\n * The human-readable name of the device, which may be set by the device's user. If the device name is unavailable, particularly on web, this value is `null`.\n *\n * > On iOS 16 and newer, this value will be set to generic \"iPhone\" until you add the correct entitlement, see [iOS Capabilities page](/build-reference/ios-capabilities)\n * > to learn how to add one and check out [Apple documentation](https://developer.apple.com/documentation/uikit/uidevice/1620015-name#discussion)\n * > for more details on this change.\n *\n * @example\n * ```js\n * Device.deviceName; // \"Vivian's iPhone XS\"\n * ```\n */\nexport const deviceName: string | null = ExpoDevice ? ExpoDevice.deviceName : null;\n\n/**\n * Checks the type of the device as a [`DeviceType`](#devicetype) enum value.\n *\n * On Android, for devices other than TVs, the device type is determined by the screen resolution (screen diagonal size), so the result may not be completely accurate.\n * If the screen diagonal length is between 3\" and 6.9\", the method returns `DeviceType.PHONE`. For lengths between 7\" and 18\", the method returns `DeviceType.TABLET`.\n * Otherwise, the method returns `DeviceType.UNKNOWN`.\n *\n * @return Returns a promise that resolves to a [`DeviceType`](#devicetype) enum value.\n * @example\n * ```js\n * await Device.getDeviceTypeAsync();\n * // DeviceType.PHONE\n * ```\n */\nexport async function getDeviceTypeAsync(): Promise<DeviceType> {\n  if (!ExpoDevice.getDeviceTypeAsync) {\n    throw new UnavailabilityError('expo-device', 'getDeviceTypeAsync');\n  }\n  return await ExpoDevice.getDeviceTypeAsync();\n}\n\n/**\n * Gets the uptime since the last reboot of the device, in milliseconds. Android devices do not count time spent in deep sleep.\n * @return Returns a promise that resolves to a `number` that represents the milliseconds since last reboot.\n * @example\n * ```js\n * await Device.getUptimeAsync();\n * // 4371054\n * ```\n * @platform android\n * @platform ios\n */\nexport async function getUptimeAsync(): Promise<number> {\n  if (!ExpoDevice.getUptimeAsync) {\n    throw new UnavailabilityError('expo-device', 'getUptimeAsync');\n  }\n  return await ExpoDevice.getUptimeAsync();\n}\n\n/**\n * Returns the maximum amount of memory that the Java VM will attempt to use. If there is no inherent limit then `Number.MAX_SAFE_INTEGER` is returned.\n * @return Returns a promise that resolves to the maximum available memory that the Java VM will use, in bytes.\n * @example\n * ```js\n * await Device.getMaxMemoryAsync();\n * // 402653184\n * ```\n * @platform android\n */\nexport async function getMaxMemoryAsync(): Promise<number> {\n  if (!ExpoDevice.getMaxMemoryAsync) {\n    throw new UnavailabilityError('expo-device', 'getMaxMemoryAsync');\n  }\n  let maxMemory = await ExpoDevice.getMaxMemoryAsync();\n  if (maxMemory === -1) {\n    maxMemory = Number.MAX_SAFE_INTEGER;\n  }\n  return maxMemory;\n}\n\n/**\n * > **warning** This method is experimental and is not completely reliable. See description below.\n *\n * Checks whether the device has been rooted (Android) or jailbroken (iOS). This is not completely reliable because there exist solutions to bypass root-detection\n * on both [iOS](https://www.theiphonewiki.com/wiki/XCon) and [Android](https://tweakerlinks.com/how-to-bypass-apps-root-detection-in-android-device/).\n * Further, many root-detection checks can be bypassed via reverse engineering.\n * - On Android, it's implemented in a way to find all possible files paths that contain the `\"su\"` executable but some devices that are not rooted may also have this executable. Therefore, there's no guarantee that this method will always return correctly.\n * - On iOS, [these jailbreak checks](https://www.theiphonewiki.com/wiki/Bypassing_Jailbreak_Detection) are used to detect if a device is rooted/jailbroken. However, since there are closed-sourced solutions such as [xCon](https://www.theiphonewiki.com/wiki/XCon) that aim to hook every known method and function responsible for informing an application of a jailbroken device, this method may not reliably detect devices that have xCon or similar packages installed.\n * - On web, this always resolves to `false` even if the device is rooted.\n * @return Returns a promise that resolves to a `boolean` that specifies whether this device is rooted.\n * @example\n * ```js\n * await Device.isRootedExperimentalAsync();\n * // true or false\n * ```\n */\nexport async function isRootedExperimentalAsync(): Promise<boolean> {\n  if (!ExpoDevice.isRootedExperimentalAsync) {\n    throw new UnavailabilityError('expo-device', 'isRootedExperimentalAsync');\n  }\n  return await ExpoDevice.isRootedExperimentalAsync();\n}\n\n/**\n * **Using this method requires you to [add the `REQUEST_INSTALL_PACKAGES` permission](./../config/app/#permissions).**\n * Returns whether applications can be installed for this user via the system's [`ACTION_INSTALL_PACKAGE`](https://developer.android.com/reference/android/content/Intent.html#ACTION_INSTALL_PACKAGE)\n * mechanism rather than through the OS's default app store, like Google Play.\n * @return Returns a promise that resolves to a `boolean` that represents whether the calling package is allowed to request package installation.\n * @example\n * ```js\n * await Device.isSideLoadingEnabledAsync();\n * // true or false\n * ```\n * @platform android\n */\nexport async function isSideLoadingEnabledAsync(): Promise<boolean> {\n  if (!ExpoDevice.isSideLoadingEnabledAsync) {\n    throw new UnavailabilityError('expo-device', 'isSideLoadingEnabledAsync');\n  }\n  return await ExpoDevice.isSideLoadingEnabledAsync();\n}\n\n/**\n * Gets a list of features that are available on the system. The feature names are platform-specific.\n * See [Android documentation](<https://developer.android.com/reference/android/content/pm/PackageManager#getSystemAvailableFeatures()>)\n * to learn more about this implementation.\n * @return Returns a promise that resolves to an array of strings, each of which is a platform-specific name of a feature available on the current device.\n * On iOS and web, the promise always resolves to an empty array.\n * @example\n * ```js\n * await Device.getPlatformFeaturesAsync();\n * // [\n * //   'android.software.adoptable_storage',\n * //   'android.software.backup',\n * //   'android.hardware.sensor.accelerometer',\n * //   'android.hardware.touchscreen',\n * // ]\n * ```\n * @platform android\n */\nexport async function getPlatformFeaturesAsync(): Promise<string[]> {\n  if (!ExpoDevice.getPlatformFeaturesAsync) {\n    return [];\n  }\n  return await ExpoDevice.getPlatformFeaturesAsync();\n}\n\n/**\n * Tells if the device has a specific system feature.\n * @param feature The platform-specific name of the feature to check for on the device. You can get all available system features with `Device.getSystemFeatureAsync()`.\n * See [Android documentation](<https://developer.android.com/reference/android/content/pm/PackageManager#hasSystemFeature(java.lang.String)>) to view acceptable feature strings.\n * @return Returns a promise that resolves to a boolean value indicating whether the device has the specified system feature.\n * On iOS and web, the promise always resolves to `false`.\n * @example\n * ```js\n * await Device.hasPlatformFeatureAsync('amazon.hardware.fire_tv');\n * // true or false\n * ```\n * @platform android\n */\nexport async function hasPlatformFeatureAsync(feature: string): Promise<boolean> {\n  if (!ExpoDevice.hasPlatformFeatureAsync) {\n    return false;\n  }\n  return await ExpoDevice.hasPlatformFeatureAsync(feature);\n}\n"]}