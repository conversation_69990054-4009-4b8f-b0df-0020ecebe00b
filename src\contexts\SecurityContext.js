import React, { createContext, useContext, useReducer, useEffect } from 'react';
import SecurityService from '../services/SecurityService';
import AuthService from '../services/AuthService';

// Security Context
const SecurityContext = createContext();

// Security Actions
const SECURITY_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_AUTHENTICATED: 'SET_AUTHENTICATED',
  SET_USER: 'SET_USER',
  SET_BIOMETRIC_AVAILABLE: 'SET_BIOMETRIC_AVAILABLE',
  SET_SECURITY_LEVEL: 'SET_SECURITY_LEVEL',
  SET_DEVICE_SECURE: 'SET_DEVICE_SECURE',
  SET_SESSION_VALID: 'SET_SESSION_VALID',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_VERIFICATION_STATUS: 'SET_VERIFICATION_STATUS'
};

// Initial Security State
const initialState = {
  isLoading: true,
  isAuthenticated: false,
  user: null,
  biometricAvailable: false,
  biometricTypes: [],
  securityLevel: 'low', // low, medium, high, maximum
  deviceSecure: false,
  sessionValid: false,
  verificationStatus: { verified: false },
  error: null,
  securityLogs: []
};

// Security Reducer
function securityReducer(state, action) {
  switch (action.type) {
    case SECURITY_ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload };
    
    case SECURITY_ACTIONS.SET_AUTHENTICATED:
      return { ...state, isAuthenticated: action.payload };
    
    case SECURITY_ACTIONS.SET_USER:
      return { ...state, user: action.payload };
    
    case SECURITY_ACTIONS.SET_BIOMETRIC_AVAILABLE:
      return { 
        ...state, 
        biometricAvailable: action.payload.available,
        biometricTypes: action.payload.types 
      };
    
    case SECURITY_ACTIONS.SET_SECURITY_LEVEL:
      return { ...state, securityLevel: action.payload };
    
    case SECURITY_ACTIONS.SET_DEVICE_SECURE:
      return { ...state, deviceSecure: action.payload };
    
    case SECURITY_ACTIONS.SET_SESSION_VALID:
      return { ...state, sessionValid: action.payload };
    
    case SECURITY_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload };
    
    case SECURITY_ACTIONS.CLEAR_ERROR:
      return { ...state, error: null };
    
    case SECURITY_ACTIONS.SET_VERIFICATION_STATUS:
      return { ...state, verificationStatus: action.payload };
    
    default:
      return state;
  }
}

// Security Provider Component
export function SecurityProvider({ children }) {
  const [state, dispatch] = useReducer(securityReducer, initialState);

  // Initialize security on app start
  useEffect(() => {
    initializeSecurity();
  }, []);

  // Set up auth listener
  useEffect(() => {
    const unsubscribe = AuthService.addAuthListener((event, user) => {
      dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: user });
      dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: !!user });
      
      if (user) {
        checkVerificationStatus(user.id);
      }
    });

    return unsubscribe;
  }, []);

  // Initialize security checks
  const initializeSecurity = async () => {
    try {
      dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: true });

      // Check device security
      const deviceFingerprint = await SecurityService.generateDeviceFingerprint();
      const deviceSecure = deviceFingerprint && !deviceFingerprint.deviceInfo.isRooted;
      dispatch({ type: SECURITY_ACTIONS.SET_DEVICE_SECURE, payload: deviceSecure });

      // Check biometric availability
      const biometricStatus = await SecurityService.isBiometricAvailable();
      dispatch({ type: SECURITY_ACTIONS.SET_BIOMETRIC_AVAILABLE, payload: biometricStatus });

      // Check session validity
      const sessionValid = await AuthService.isAuthenticated();
      dispatch({ type: SECURITY_ACTIONS.SET_SESSION_VALID, payload: sessionValid });

      // Calculate security level
      const securityLevel = calculateSecurityLevel(deviceSecure, biometricStatus.available, sessionValid);
      dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_LEVEL, payload: securityLevel });

      // Get current user if authenticated
      if (sessionValid) {
        const currentUser = AuthService.getCurrentUser();
        dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: currentUser });
        dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: true });
        
        if (currentUser) {
          await checkVerificationStatus(currentUser.id);
        }
      }

    } catch (error) {
      console.error('Security initialization failed:', error);
      dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: error.message });
    } finally {
      dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Calculate overall security level
  const calculateSecurityLevel = (deviceSecure, biometricAvailable, sessionValid) => {
    let score = 0;
    
    if (deviceSecure) score += 25;
    if (biometricAvailable) score += 25;
    if (sessionValid) score += 25;
    
    // Additional factors
    score += 25; // Base security measures
    
    if (score >= 90) return 'maximum';
    if (score >= 70) return 'high';
    if (score >= 50) return 'medium';
    return 'low';
  };

  // Check verification status
  const checkVerificationStatus = async (userId) => {
    try {
      const verificationStatus = await AuthService.getVerificationStatus(userId);
      dispatch({ type: SECURITY_ACTIONS.SET_VERIFICATION_STATUS, payload: verificationStatus });
    } catch (error) {
      console.error('Failed to check verification status:', error);
    }
  };

  // Security Actions
  const securityActions = {
    // Authentication
    login: async (email, password, options = {}) => {
      try {
        dispatch({ type: SECURITY_ACTIONS.CLEAR_ERROR });
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: true });

        const result = await AuthService.login(email, password, options);
        
        if (result.success) {
          dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: result.user });
          dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: true });
          dispatch({ type: SECURITY_ACTIONS.SET_SESSION_VALID, payload: true });
          
          await checkVerificationStatus(result.user.id);
          
          // Recalculate security level
          const securityLevel = calculateSecurityLevel(
            state.deviceSecure, 
            state.biometricAvailable, 
            true
          );
          dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_LEVEL, payload: securityLevel });
        } else {
          dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: result.error });
        }

        return result;
      } catch (error) {
        dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: error.message });
        return { success: false, error: error.message };
      } finally {
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: false });
      }
    },

    // Biometric login
    loginWithBiometric: async () => {
      try {
        dispatch({ type: SECURITY_ACTIONS.CLEAR_ERROR });
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: true });

        const result = await AuthService.loginWithBiometric();
        
        if (result.success) {
          dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: result.user });
          dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: true });
          dispatch({ type: SECURITY_ACTIONS.SET_SESSION_VALID, payload: true });
          
          await checkVerificationStatus(result.user.id);
        } else {
          dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: result.error });
        }

        return result;
      } catch (error) {
        dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: error.message });
        return { success: false, error: error.message };
      } finally {
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: false });
      }
    },

    // Registration
    register: async (userData) => {
      try {
        dispatch({ type: SECURITY_ACTIONS.CLEAR_ERROR });
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: true });

        const result = await AuthService.register(userData);
        
        if (result.success) {
          dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: result.user });
          dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: true });
          
          // Auto-grant verification for new users
          await requestVerification(result.user.id);
        } else {
          dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: result.error });
        }

        return result;
      } catch (error) {
        dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: error.message });
        return { success: false, error: error.message };
      } finally {
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: false });
      }
    },

    // Logout
    logout: async () => {
      try {
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: true });

        const result = await AuthService.logout();
        
        if (result.success) {
          dispatch({ type: SECURITY_ACTIONS.SET_USER, payload: null });
          dispatch({ type: SECURITY_ACTIONS.SET_AUTHENTICATED, payload: false });
          dispatch({ type: SECURITY_ACTIONS.SET_SESSION_VALID, payload: false });
          dispatch({ type: SECURITY_ACTIONS.SET_VERIFICATION_STATUS, payload: { verified: false } });
          
          // Recalculate security level
          const securityLevel = calculateSecurityLevel(
            state.deviceSecure, 
            state.biometricAvailable, 
            false
          );
          dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_LEVEL, payload: securityLevel });
        }

        return result;
      } catch (error) {
        dispatch({ type: SECURITY_ACTIONS.SET_ERROR, payload: error.message });
        return { success: false, error: error.message };
      } finally {
        dispatch({ type: SECURITY_ACTIONS.SET_LOADING, payload: false });
      }
    },

    // Request verification
    requestVerification: async (userId, verificationData = {}) => {
      try {
        const result = await AuthService.requestVerification(userId, verificationData);
        
        if (result.success) {
          dispatch({ type: SECURITY_ACTIONS.SET_VERIFICATION_STATUS, payload: {
            verified: true,
            badgeType: result.badgeType,
            approvedAt: new Date().toISOString()
          }});
        }

        return result;
      } catch (error) {
        console.error('Verification request failed:', error);
        return { success: false, error: error.message };
      }
    },

    // Security utilities
    clearError: () => {
      dispatch({ type: SECURITY_ACTIONS.CLEAR_ERROR });
    },

    refreshSecurityStatus: async () => {
      await initializeSecurity();
    },

    getSecurityLogs: async () => {
      try {
        const logs = await SecurityService.getSecurityLogs();
        return logs;
      } catch (error) {
        console.error('Failed to get security logs:', error);
        return [];
      }
    },

    // Biometric setup
    setupBiometric: async () => {
      try {
        const biometricStatus = await SecurityService.isBiometricAvailable();
        dispatch({ type: SECURITY_ACTIONS.SET_BIOMETRIC_AVAILABLE, payload: biometricStatus });
        
        if (biometricStatus.available) {
          // Test biometric authentication
          const authResult = await SecurityService.authenticateWithBiometrics(
            'Set up biometric authentication for Africa Social'
          );
          return authResult;
        }
        
        return { success: false, error: 'Biometric authentication not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }
  };

  // Request verification helper
  const requestVerification = async (userId) => {
    return securityActions.requestVerification(userId);
  };

  const contextValue = {
    // State
    ...state,
    
    // Actions
    ...securityActions,
    
    // Computed values
    isSecure: state.securityLevel === 'high' || state.securityLevel === 'maximum',
    canUseBiometric: state.biometricAvailable && state.deviceSecure,
    securityScore: {
      low: 25,
      medium: 50,
      high: 75,
      maximum: 100
    }[state.securityLevel] || 0
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
}

// Custom hook to use security context
export function useSecurity() {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
}

// HOC for protected components
export function withSecurity(Component) {
  return function SecurityWrappedComponent(props) {
    const security = useSecurity();
    
    if (!security.isAuthenticated) {
      return null; // or redirect to login
    }
    
    return <Component {...props} security={security} />;
  };
}

export default SecurityContext;
