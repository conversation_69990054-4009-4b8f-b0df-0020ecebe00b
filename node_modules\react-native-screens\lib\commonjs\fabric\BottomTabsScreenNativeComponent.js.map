{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_codegenNativeComponent", "_interopRequireDefault", "require", "e", "__esModule", "_default", "codegenNativeComponent"], "sourceRoot": "../../../src", "sources": ["fabric/BottomTabsScreenNativeComponent.ts"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAJ,OAAA,EAAAI,CAAA;AAS7F;AAGA;AAGA;AAAA,IAAAE,QAAA,GAAAR,OAAA,CAAAE,OAAA,GAyFe,IAAAO,+BAAsB,EAAc,qBAAqB,EAAE,CAAC,CAAC,CAAC", "ignoreList": []}