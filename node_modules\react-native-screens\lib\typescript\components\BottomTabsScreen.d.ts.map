{"version": 3, "file": "BottomTabsScreen.d.ts", "sourceRoot": "", "sources": ["../../../src/components/BottomTabsScreen.tsx"], "names": [], "mappings": "AAEA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAwC,EACtC,UAAU,EAGX,MAAM,2CAA2C,CAAC;AACnD,OAAO,EACL,KAAK,UAAU,EACf,KAAK,mBAAmB,EACxB,KAAK,oBAAoB,EAEzB,SAAS,EACT,KAAK,SAAS,EAEf,MAAM,cAAc,CAAC;AAKtB,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAChD,MAAM,MAAM,4BAA4B,CAAC,CAAC,IAAI,CAC5C,KAAK,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAC3B,IAAI,CAAC;AAGV,MAAM,WAAW,MAAM;IACrB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,SAAS;IACxB,WAAW,EAAE,mBAAmB,CAAC;CAClC;AAGD,MAAM,WAAW,YAAY;IAC3B,cAAc,EAAE,mBAAmB,CAAC;CACrC;AAGD,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,SAAS,GAAG,YAAY,CAAC;AAErD,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;IACjC,WAAW,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;IAK1C,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC;IAIf,qBAAqB,CAAC,EAAE,UAAU,CAAC;IACnC,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAE9B,yBAAyB,CAAC,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IACpD,uBAAuB,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;IAChD,yBAAyB,CAAC,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IACpD,wBAAwB,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;IAClD,wBAAwB,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;IAC9C,iCAAiC,CAAC,EAAE;QAClC,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;IAEF,mBAAmB,CAAC,EAAE,UAAU,CAAC;IAEjC,8BAA8B,CAAC,EAAE,UAAU,CAAC;IAG5C,KAAK,CAAC,EAAE,MAAM,CAAC;IAGf,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B,IAAI,CAAC,EAAE,IAAI,CAAC;IACZ,YAAY,CAAC,EAAE,IAAI,CAAC;IAEpB,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,cAAc,CAAC,EAAE;QACf,oBAAoB,CAAC,EAAE;YACrB,SAAS,CAAC,EAAE,OAAO,CAAC;YACpB,WAAW,CAAC,EAAE,OAAO,CAAC;SACvB,CAAC;KACH,CAAC;IAEF,gDAAgD,CAAC,EAAE,OAAO,CAAC;IAG3D,YAAY,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,CAAC;IACzD,WAAW,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,CAAC;IACxD,eAAe,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,CAAC;IAC5D,cAAc,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,CAAC;CAC5D;AAED;;GAEG;AACH,iBAAS,gBAAgB,CAAC,KAAK,EAAE,qBAAqB,qBAuGrD;AA4ED,eAAe,gBAAgB,CAAC"}