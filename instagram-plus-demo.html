<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram + - Social Media with Free Verification</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .tagline {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #228B22;
        }
        
        .phone-mockup {
            width: 300px;
            height: 600px;
            background: #000;
            border-radius: 30px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .app-header {
            background: #228B22;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .post {
            border-bottom: 1px solid #eee;
            padding: 15px;
        }
        
        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #228B22, #32CD32);
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .username {
            font-weight: bold;
            margin-right: 5px;
        }
        
        .verified-badge {
            color: #00C851;
            font-size: 16px;
        }
        
        .post-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #228B22, #32CD32);
            border-radius: 10px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .post-actions {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        
        .action-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #333;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #228B22;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.5;
        }
        
        .security-dashboard {
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .security-level {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .security-badge {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .security-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .security-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .download-section {
            text-align: center;
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            border-radius: 15px;
            padding: 40px;
        }
        
        .download-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .download-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            border-top: 1px solid #eee;
        }
        
        .tab-item {
            text-align: center;
            font-size: 20px;
            color: #666;
        }
        
        .tab-item.active {
            color: #228B22;
        }
        
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .phone-mockup {
                width: 250px;
                height: 500px;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">📱 Instagram +</div>
        <div class="tagline">Instagram with Free Verification & Advanced Security</div>
    </div>
    
    <div class="container">
        <!-- App Demo Section -->
        <div class="demo-section">
            <h2 class="section-title">🚀 Live App Demo</h2>
            <div style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap;">
                <div class="phone-mockup">
                    <div class="screen">
                        <div class="app-header">Instagram +</div>
                        
                        <div class="post">
                            <div class="post-header">
                                <div class="avatar">AM</div>
                                <div>
                                    <span class="username">amara_lagos</span>
                                    <span class="verified-badge">✓</span>
                                    <div style="font-size: 12px; color: #666;">2h ago</div>
                                </div>
                            </div>
                            <div class="post-image">📸 Beautiful Lagos Sunset</div>
                            <div class="post-actions">
                                <button class="action-btn">❤️</button>
                                <button class="action-btn">💬</button>
                                <button class="action-btn">📤</button>
                            </div>
                            <div style="font-size: 14px;">
                                <strong>234 likes</strong><br>
                                <strong>amara_lagos</strong> Amazing view from my balcony! 🌅
                            </div>
                        </div>
                        
                        <div class="post">
                            <div class="post-header">
                                <div class="avatar">KA</div>
                                <div>
                                    <span class="username">kwame_accra</span>
                                    <span class="verified-badge">✓</span>
                                    <div style="font-size: 12px; color: #666;">4h ago</div>
                                </div>
                            </div>
                            <div class="post-image">🎨 Traditional Art</div>
                            <div class="post-actions">
                                <button class="action-btn">❤️</button>
                                <button class="action-btn">💬</button>
                                <button class="action-btn">📤</button>
                            </div>
                            <div style="font-size: 14px;">
                                <strong>189 likes</strong><br>
                                <strong>kwame_accra</strong> Working on my latest piece 🎨
                            </div>
                        </div>
                        
                        <div class="tab-bar">
                            <div class="tab-item active">🏠</div>
                            <div class="tab-item">🔍</div>
                            <div class="tab-item">📷</div>
                            <div class="tab-item">🔔</div>
                            <div class="tab-item">👤</div>
                        </div>
                    </div>
                </div>
                
                <div style="flex: 1; min-width: 300px;">
                    <h3 style="color: #228B22; margin-bottom: 15px;">✨ Key Features</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">✅ <strong>Free Verification Badges</strong> - No payment required!</li>
                        <li style="margin-bottom: 10px;">🔐 <strong>Advanced Security</strong> - Biometric authentication</li>
                        <li style="margin-bottom: 10px;">📱 <strong>Cross-Platform</strong> - iOS, Android, Web</li>
                        <li style="margin-bottom: 10px;">🛡️ <strong>Privacy First</strong> - End-to-end encryption</li>
                        <li style="margin-bottom: 10px;">🌍 <strong>Beautiful Design</strong> - African-inspired theme</li>
                        <li style="margin-bottom: 10px;">⚡ <strong>Enhanced Features</strong> - All Instagram features plus more</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Security Dashboard -->
        <div class="security-dashboard">
            <div class="security-level">
                <span style="font-size: 24px;">🛡️</span>
                <span style="font-size: 20px; margin-left: 10px;">MAXIMUM SECURITY</span>
                <div class="security-badge">100% SECURE</div>
            </div>
            
            <div class="security-features">
                <div class="security-item">
                    <div style="font-size: 24px; margin-bottom: 5px;">🔐</div>
                    <div><strong>Biometric Auth</strong></div>
                    <div style="font-size: 12px; opacity: 0.8;">Active</div>
                </div>
                <div class="security-item">
                    <div style="font-size: 24px; margin-bottom: 5px;">🛡️</div>
                    <div><strong>Device Security</strong></div>
                    <div style="font-size: 12px; opacity: 0.8;">Verified</div>
                </div>
                <div class="security-item">
                    <div style="font-size: 24px; margin-bottom: 5px;">🔒</div>
                    <div><strong>Encryption</strong></div>
                    <div style="font-size: 12px; opacity: 0.8;">End-to-End</div>
                </div>
                <div class="security-item">
                    <div style="font-size: 24px; margin-bottom: 5px;">👁️</div>
                    <div><strong>Monitoring</strong></div>
                    <div style="font-size: 12px; opacity: 0.8;">Real-time</div>
                </div>
            </div>
        </div>
        
        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">✅</div>
                <div class="feature-title">Free Verification</div>
                <div class="feature-desc">Get your verification badge completely free - no payment required! Unlike other platforms, we believe verification should be accessible to everyone.</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">Advanced Security</div>
                <div class="feature-desc">Enterprise-grade security with biometric authentication, device fingerprinting, and real-time fraud detection to keep your account safe.</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Cross-Platform</div>
                <div class="feature-desc">Available on iOS, Android, and Web with seamless synchronization across all your devices. Built with React Native and Expo.</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <div class="feature-title">Privacy First</div>
                <div class="feature-desc">Your data is protected with end-to-end encryption, secure storage, and privacy-focused design. We never sell your data.</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <div class="feature-title">Beautiful Design</div>
                <div class="feature-desc">African-inspired green and white theme with modern UI/UX design that celebrates culture while maintaining usability.</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Enhanced Features</div>
                <div class="feature-desc">All the Instagram features you love, plus advanced security, free verification, and enhanced privacy controls.</div>
            </div>
        </div>
        
        <!-- Download Section -->
        <div class="download-section">
            <h2 style="margin-bottom: 10px;">📲 Download Instagram +</h2>
            <p style="margin-bottom: 20px; opacity: 0.9;">Ready for deployment to both iOS App Store and Google Play Store</p>
            
            <div class="download-buttons">
                <a href="#" class="download-btn">🍎 Download for iOS</a>
                <a href="#" class="download-btn">🤖 Download for Android</a>
                <a href="#" class="download-btn">🌐 Open Web App</a>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2);">
                <p><strong>🚀 Development Status:</strong> Complete and ready for app store deployment!</p>
                <p style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                    <strong>Tech Stack:</strong> React Native • Expo • Supabase • Advanced Security • Biometric Auth • Real-time Features
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
            
            // Simulate app interactions
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);
                });
            });
            
            // Tab switching simulation
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
        
        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .feature-card {
                opacity: 0;
            }
            
            .action-btn {
                transition: transform 0.2s ease;
            }
            
            .tab-item {
                cursor: pointer;
                transition: color 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
