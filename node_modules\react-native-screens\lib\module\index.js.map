{"version": 3, "names": ["enableScreens", "enableFreeze", "screensEnabled", "freezeEnabled", "default", "Screen", "InnerScreen", "ScreenContext", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderSearchBarView", "SearchBar", "ScreenContainer", "ScreenStack", "ScreenStackItem", "FullWindowOverlay", "ScreenFooter", "ScreenContentWrapper", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "compatibilityFlags", "featureFlags", "useTransitionProgress", "BottomTabs", "BottomTabsScreen", "ScreenStackHost", "StackScreen", "StackScreenLifecycleState", "SplitViewHost", "SplitViewScreen"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA;AACA;AACA,OAAO,8BAA8B;AAErC,cAAc,SAAS;;AAEvB;AACA;AACA;AACA,SACEA,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,aAAa,QACR,QAAQ;;AAEf;AACA;AACA;AACA,SACEC,OAAO,IAAIC,MAAM,EACjBC,WAAW,EACXC,aAAa,QACR,qBAAqB;AAE5B,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB,EACzBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,8BAA8B,QACzB,sCAAsC;AAE7C,SAASV,OAAO,IAAIW,SAAS,QAAQ,wBAAwB;AAC7D,SAASX,OAAO,IAAIY,eAAe,QAAQ,8BAA8B;AACzE,SAASZ,OAAO,IAAIa,WAAW,QAAQ,0BAA0B;AACjE,SAASb,OAAO,IAAIc,eAAe,QAAQ,8BAA8B;AACzE,SAASd,OAAO,IAAIe,iBAAiB,QAAQ,gCAAgC;AAC7E,SAASf,OAAO,IAAIgB,YAAY,QAAQ,2BAA2B;AACnE,SAAShB,OAAO,IAAIiB,oBAAoB,QAAQ,mCAAmC;;AAEnF;AACA;AACA;AACA,SACEC,sCAAsC,EACtCC,sBAAsB,QACjB,SAAS;;AAEhB;AACA;AACA;AACA,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,SAAS;;AAE1D;AACA;AACA;AACA,SAASrB,OAAO,IAAIsB,qBAAqB,QAAQ,yBAAyB;;AAE1E;AACA;AACA;AACA,SAAStB,OAAO,IAAIuB,UAAU,QAAQ,yBAAyB;AAC/D,SAASvB,OAAO,IAAIwB,gBAAgB,QAAQ,+BAA+B;AAC3E,SAASxB,OAAO,IAAIyB,eAAe,QAAQ,oCAAoC;AAC/E,SACEzB,OAAO,IAAI0B,WAAW,EACtBC,yBAAyB,QACpB,gCAAgC;AACvC,SAAS3B,OAAO,IAAI4B,aAAa,QAAQ,kCAAkC;AAC3E,SAAS5B,OAAO,IAAI6B,eAAe,QAAQ,oCAAoC", "ignoreList": []}