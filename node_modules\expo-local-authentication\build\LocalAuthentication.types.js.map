{"version": 3, "file": "LocalAuthentication.types.js", "sourceRoot": "", "sources": ["../src/LocalAuthentication.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAU7C,cAAc;AACd,MAAM,CAAN,IAAY,kBAcX;AAdD,WAAY,kBAAkB;IAC5B;;OAEG;IACH,yEAAe,CAAA;IACf;;OAEG;IACH,uFAAsB,CAAA;IACtB;;;OAGG;IACH,2DAAQ,CAAA;AACV,CAAC,EAdW,kBAAkB,KAAlB,kBAAkB,QAc7B;AAED,cAAc;AACd,MAAM,CAAN,IAAY,aA0BX;AA1BD,WAAY,aAAa;IACvB;;OAEG;IACH,iDAAQ,CAAA;IACR;;OAEG;IACH,qDAAU,CAAA;IACV;;;;OAIG;IACH,2CAAY,QAAQ,CAAC,EAAE,KAAK,SAAS;QACnC,CAAC,CAAC,aAAa,CAAC,cAAc;QAC9B,CAAC,CAAC,aAAa,CAAC,gBAAgB,eAAA,CAAA;IAClC;;;OAGG;IACH,qEAAkB,CAAA;IAClB;;OAEG;IACH,yEAAoB,CAAA;AACtB,CAAC,EA1BW,aAAa,KAAb,aAAa,QA0BxB;AAED,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,WAAW,EAAE;IAChD,GAAG;QACD,MAAM,iBAAiB,GACrB,QAAQ,CAAC,EAAE,KAAK,SAAS;YACvB,CAAC,CAAC,4IAA4I;YAC9I,CAAC,CAAC,EAAE,CAAC;QACT,OAAO,CAAC,IAAI,CACV,+HAA+H;YAC7H,iBAAiB,CACpB,CAAC;QACF,OAAO,QAAQ,CAAC,EAAE,KAAK,SAAS;YAC9B,CAAC,CAAC,aAAa,CAAC,cAAc;YAC9B,CAAC,CAAC,aAAa,CAAC,gBAAgB,CAAC;IACrC,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nexport type LocalAuthenticationResult =\n  | { success: true }\n  | {\n      success: false;\n      error: LocalAuthenticationError;\n      warning?: string;\n    };\n\n// @needsAudit\nexport enum AuthenticationType {\n  /**\n   * Indicates fingerprint support.\n   */\n  FINGERPRINT = 1,\n  /**\n   * Indicates facial recognition support.\n   */\n  FACIAL_RECOGNITION = 2,\n  /**\n   * Indicates iris recognition support.\n   * @platform android\n   */\n  IRIS = 3,\n}\n\n// @needsAudit\nexport enum SecurityLevel {\n  /**\n   * Indicates no enrolled authentication.\n   */\n  NONE = 0,\n  /**\n   * Indicates non-biometric authentication (e.g. P<PERSON>, Pattern).\n   */\n  SECRET = 1,\n  /**\n   * Indicates biometric authentication.\n   * @deprecated please use `BIOMETRIC_STRONG` or `BIOMETRIC_WEAK` instead.\n   * @hidden\n   */\n  BIOMETRIC = Platform.OS === 'android'\n    ? SecurityLevel.BIOMETRIC_WEAK\n    : SecurityLevel.BIOMETRIC_STRONG,\n  /**\n   * Indicates weak biometric authentication. For example, a 2D image-based face unlock.\n   * > There are currently no weak biometric authentication options on iOS.\n   */\n  BIOMETRIC_WEAK = 2,\n  /**\n   * Indicates strong biometric authentication. For example, a fingerprint scan or 3D face unlock.\n   */\n  BIOMETRIC_STRONG = 3,\n}\n\nObject.defineProperty(SecurityLevel, 'BIOMETRIC', {\n  get() {\n    const additionalMessage =\n      Platform.OS === 'android'\n        ? '. `SecurityLevel.BIOMETRIC` is currently an alias for `SecurityLevel.BIOMETRIC_WEAK` on Android, which might lead to unexpected behaviour.'\n        : '';\n    console.warn(\n      '`SecurityLevel.BIOMETRIC` has been deprecated. Use `SecurityLevel.BIOMETRIC_WEAK` or `SecurityLevel.BIOMETRIC_STRONG` instead' +\n        additionalMessage\n    );\n    return Platform.OS === 'android'\n      ? SecurityLevel.BIOMETRIC_WEAK\n      : SecurityLevel.BIOMETRIC_STRONG;\n  },\n});\n\n/**\n * Security level of the biometric authentication to allow.\n * @platform android\n */\nexport type BiometricsSecurityLevel = 'weak' | 'strong';\n\n// @needsAudit\nexport type LocalAuthenticationOptions = {\n  /**\n   * A message that is shown alongside the TouchID or FaceID prompt.\n   */\n  promptMessage?: string;\n  /**\n   * Allows to customize the default `Cancel` label shown.\n   */\n  cancelLabel?: string;\n  /**\n   * After several failed attempts the system will fallback to the device passcode. This setting\n   * allows you to disable this option and instead handle the fallback yourself. This can be\n   * preferable in certain custom authentication workflows. This behaviour maps to using the iOS\n   * [`LAPolicyDeviceOwnerAuthenticationWithBiometrics`](https://developer.apple.com/documentation/localauthentication/lapolicy/deviceownerauthenticationwithbiometrics)\n   * policy rather than the [`LAPolicyDeviceOwnerAuthentication`](https://developer.apple.com/documentation/localauthentication/lapolicy/deviceownerauthentication?language=objc)\n   * policy. Defaults to `false`.\n   */\n  disableDeviceFallback?: boolean;\n  /**\n   * Sets a hint to the system for whether to require user confirmation after authentication.\n   * This may be ignored by the system if the user has disabled implicit authentication in Settings\n   * or if it does not apply to a particular biometric modality. Defaults to `true`.\n   * @platform android\n   */\n  requireConfirmation?: boolean;\n  /**\n   * Sets the security class of biometric authentication to allow.\n   * `strong` allows only Android Class 3 biometrics. For example, a fingerprint or a 3D face scan.\n   * `weak` allows both Android Class 3 and Class 2 biometrics. Class 2 biometrics are less secure than Class 3. For example, a camera-based face unlock.\n   * @platform android\n   * @default 'weak'\n   */\n  biometricsSecurityLevel?: BiometricsSecurityLevel;\n  /**\n   * Allows to customize the default `Use Passcode` label shown after several failed\n   * authentication attempts. Setting this option to an empty string disables this button from\n   * showing in the prompt.\n   * @platform ios\n   */\n  fallbackLabel?: string;\n};\n\n/**\n * One of the error values returned by the [`LocalAuthenticationResult`](#localauthenticationresult) object.\n */\nexport type LocalAuthenticationError =\n  | 'not_enrolled'\n  | 'user_cancel'\n  | 'app_cancel'\n  | 'not_available'\n  | 'lockout'\n  | 'no_space'\n  | 'timeout'\n  | 'unable_to_process'\n  | 'unknown'\n  | 'system_cancel'\n  | 'user_fallback'\n  | 'invalid_context'\n  | 'passcode_not_set'\n  | 'authentication_failed';\n"]}