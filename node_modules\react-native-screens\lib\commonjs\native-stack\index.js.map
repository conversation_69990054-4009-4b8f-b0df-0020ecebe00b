{"version": 3, "names": ["_createNativeStackNavigator", "_interopRequireDefault", "require", "_NativeStackView", "_useHeaderHeight", "_HeaderHeightContext", "_useAnimatedHeaderHeight", "_AnimatedHeaderHeightContext", "e", "__esModule", "default"], "sourceRoot": "../../../src", "sources": ["native-stack/index.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,2BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,gBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,oBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAAI,wBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,4BAAA,GAAAN,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA", "ignoreList": []}