import React from 'react';
import { BlurEffect, NativeFocusChangeEvent } from '../fabric/BottomTabsNativeComponent';
import { type ColorValue, type NativeSyntheticEvent, TextStyle, type ViewProps } from 'react-native';
export interface BottomTabsProps extends ViewProps {
    onNativeFocusChange?: (event: NativeSyntheticEvent<NativeFocusChangeEvent>) => void;
    tabBarBackgroundColor?: ColorValue;
    tabBarBlurEffect?: BlurEffect;
    tabBarTintColor?: ColorValue;
    tabBarItemTitleFontFamily?: TextStyle['fontFamily'];
    tabBarItemTitleFontSize?: TextStyle['fontSize'];
    tabBarItemTitleFontWeight?: TextStyle['fontWeight'];
    tabBarItemTitleFontStyle?: TextStyle['fontStyle'];
    tabBarItemTitleFontColor?: TextStyle['color'];
    tabBarItemTitlePositionAdjustment?: {
        horizontal?: number;
        vertical?: number;
    };
    tabBarItemIconColor?: ColorValue;
    tabBarItemBadgeBackgroundColor?: ColorValue;
    tabBarItemTitleFontSizeActive?: TextStyle['fontSize'];
    tabBarItemTitleFontColorActive?: TextStyle['color'];
    tabBarItemIconColorActive?: ColorValue;
    experimentalControlNavigationStateInJS?: boolean;
}
/**
 * EXPERIMENTAL API, MIGHT CHANGE W/O ANY NOTICE
 */
declare function BottomTabs(props: BottomTabsProps): React.JSX.Element;
export default BottomTabs;
//# sourceMappingURL=BottomTabs.d.ts.map