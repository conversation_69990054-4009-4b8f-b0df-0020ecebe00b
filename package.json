{"name": "africa-social", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "keywords": ["react-native", "expo", "social-media", "africa"], "author": "Africa Social Team", "license": "MIT", "description": "A social media app with free verification badges, inspired by African colors and culture", "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.52.0", "babel-preset-expo": "^13.2.3", "expo": "^53.0.20", "expo-application": "^6.1.5", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-crypto": "^14.1.5", "expo-device": "^7.1.4", "expo-image-picker": "^16.1.4", "expo-local-authentication": "^16.0.5", "expo-media-library": "^17.1.7", "expo-secure-store": "^14.2.3", "react": "^19.1.0", "react-dom": "19.0.0", "react-native": "^0.80.1", "react-native-elements": "^3.4.3", "react-native-keychain": "^10.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}}