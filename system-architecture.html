<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram + System Architecture</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 36px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .architecture-diagram {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .layer {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }
        
        .layer-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            color: white;
        }
        
        .client-layer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .api-layer {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .security-layer {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .business-layer {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .data-layer {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .infrastructure-layer {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .components {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .component {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .component:hover {
            transform: translateY(-3px);
        }
        
        .component-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .component-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .component-desc {
            font-size: 12px;
            color: #666;
        }
        
        .flow-arrow {
            text-align: center;
            font-size: 24px;
            color: #228B22;
            margin: 10px 0;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .tech-category {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .tech-category h3 {
            color: #228B22;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .tech-list {
            list-style: none;
        }
        
        .tech-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tech-list li:last-child {
            border-bottom: none;
        }
        
        .tech-badge {
            background: #228B22;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .security-features {
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .security-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .metric-number {
            font-size: 32px;
            font-weight: bold;
            color: #228B22;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .components {
                grid-template-columns: 1fr;
            }
            
            .tech-stack {
                grid-template-columns: 1fr;
            }
            
            .security-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏗️ Instagram + System Architecture</h1>
        <p>Enterprise-grade social media platform with advanced security and free verification</p>
    </div>
    
    <div class="container">
        <!-- Architecture Diagram -->
        <div class="architecture-diagram">
            <h2 style="text-align: center; margin-bottom: 30px; color: #228B22;">System Architecture Overview</h2>
            
            <!-- Client Layer -->
            <div class="layer">
                <div class="layer-title client-layer">📱 Client Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">📱</div>
                        <div class="component-title">iOS App</div>
                        <div class="component-desc">React Native + Expo</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🤖</div>
                        <div class="component-title">Android App</div>
                        <div class="component-desc">React Native + Expo</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🌐</div>
                        <div class="component-title">Web App</div>
                        <div class="component-desc">Progressive Web App</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">💻</div>
                        <div class="component-title">Admin Dashboard</div>
                        <div class="component-desc">React + TypeScript</div>
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">⬇️</div>
            
            <!-- API Gateway Layer -->
            <div class="layer">
                <div class="layer-title api-layer">🚪 API Gateway Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">🔗</div>
                        <div class="component-title">REST API</div>
                        <div class="component-desc">Express.js + TypeScript</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">📡</div>
                        <div class="component-title">GraphQL API</div>
                        <div class="component-desc">Apollo Server</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">⚡</div>
                        <div class="component-title">WebSocket</div>
                        <div class="component-desc">Real-time features</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🛡️</div>
                        <div class="component-title">Rate Limiter</div>
                        <div class="component-desc">Redis-based</div>
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">⬇️</div>
            
            <!-- Security Layer -->
            <div class="layer">
                <div class="layer-title security-layer">🔐 Security Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">🔑</div>
                        <div class="component-title">Auth Service</div>
                        <div class="component-desc">JWT + Biometric</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🛡️</div>
                        <div class="component-title">Security Scanner</div>
                        <div class="component-desc">Device fingerprinting</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🔒</div>
                        <div class="component-title">Encryption</div>
                        <div class="component-desc">AES-256 + RSA</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">👁️</div>
                        <div class="component-title">Fraud Detection</div>
                        <div class="component-desc">ML-powered</div>
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">⬇️</div>
            
            <!-- Business Logic Layer -->
            <div class="layer">
                <div class="layer-title business-layer">⚙️ Business Logic Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">👥</div>
                        <div class="component-title">User Service</div>
                        <div class="component-desc">Profile management</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">📸</div>
                        <div class="component-title">Content Service</div>
                        <div class="component-desc">Posts & media</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">✅</div>
                        <div class="component-title">Verification Service</div>
                        <div class="component-desc">Free badge system</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🔔</div>
                        <div class="component-title">Notification Service</div>
                        <div class="component-desc">Push notifications</div>
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">⬇️</div>
            
            <!-- Data Layer -->
            <div class="layer">
                <div class="layer-title data-layer">💾 Data Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">🐘</div>
                        <div class="component-title">PostgreSQL</div>
                        <div class="component-desc">Primary database</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">⚡</div>
                        <div class="component-title">Redis</div>
                        <div class="component-desc">Cache & sessions</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">📊</div>
                        <div class="component-title">ClickHouse</div>
                        <div class="component-desc">Analytics data</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🗄️</div>
                        <div class="component-title">S3 Storage</div>
                        <div class="component-desc">Media files</div>
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">⬇️</div>
            
            <!-- Infrastructure Layer -->
            <div class="layer">
                <div class="layer-title infrastructure-layer">☁️ Infrastructure Layer</div>
                <div class="components">
                    <div class="component">
                        <div class="component-icon">🚀</div>
                        <div class="component-title">Kubernetes</div>
                        <div class="component-desc">Container orchestration</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🌐</div>
                        <div class="component-title">CDN</div>
                        <div class="component-desc">Global content delivery</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">📊</div>
                        <div class="component-title">Monitoring</div>
                        <div class="component-desc">Prometheus + Grafana</div>
                    </div>
                    <div class="component">
                        <div class="component-icon">🔄</div>
                        <div class="component-title">CI/CD</div>
                        <div class="component-desc">GitHub Actions</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Security Features -->
        <div class="security-features">
            <h2 style="text-align: center; margin-bottom: 20px;">🛡️ Advanced Security Features</h2>
            <div class="security-grid">
                <div class="security-item">
                    <div style="font-size: 32px; margin-bottom: 10px;">🔐</div>
                    <h3>Multi-Factor Authentication</h3>
                    <p>Biometric + Password + Device verification</p>
                </div>
                <div class="security-item">
                    <div style="font-size: 32px; margin-bottom: 10px;">🔒</div>
                    <h3>End-to-End Encryption</h3>
                    <p>AES-256 encryption for all sensitive data</p>
                </div>
                <div class="security-item">
                    <div style="font-size: 32px; margin-bottom: 10px;">👁️</div>
                    <h3>Real-time Monitoring</h3>
                    <p>24/7 threat detection and prevention</p>
                </div>
                <div class="security-item">
                    <div style="font-size: 32px; margin-bottom: 10px;">🛡️</div>
                    <h3>Device Security</h3>
                    <p>Root/jailbreak detection and prevention</p>
                </div>
            </div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-number">99.9%</div>
                <div class="metric-label">Uptime SLA</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">&lt;100ms</div>
                <div class="metric-label">API Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">10M+</div>
                <div class="metric-label">Requests/Day</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">50+</div>
                <div class="metric-label">Global Regions</div>
            </div>
        </div>
        
        <!-- Technology Stack -->
        <div class="tech-stack">
            <div class="tech-category">
                <h3>🎨 Frontend Technologies</h3>
                <ul class="tech-list">
                    <li>React Native <span class="tech-badge">MOBILE</span></li>
                    <li>Expo SDK <span class="tech-badge">CROSS-PLATFORM</span></li>
                    <li>TypeScript <span class="tech-badge">TYPE-SAFE</span></li>
                    <li>React Navigation <span class="tech-badge">ROUTING</span></li>
                    <li>React Query <span class="tech-badge">DATA</span></li>
                </ul>
            </div>
            
            <div class="tech-category">
                <h3>⚙️ Backend Technologies</h3>
                <ul class="tech-list">
                    <li>Node.js + Express <span class="tech-badge">API</span></li>
                    <li>GraphQL + Apollo <span class="tech-badge">QUERY</span></li>
                    <li>PostgreSQL <span class="tech-badge">DATABASE</span></li>
                    <li>Redis <span class="tech-badge">CACHE</span></li>
                    <li>Socket.io <span class="tech-badge">REALTIME</span></li>
                </ul>
            </div>
            
            <div class="tech-category">
                <h3>🔐 Security Technologies</h3>
                <ul class="tech-list">
                    <li>JWT + Refresh Tokens <span class="tech-badge">AUTH</span></li>
                    <li>Biometric Authentication <span class="tech-badge">BIOMETRIC</span></li>
                    <li>AES-256 Encryption <span class="tech-badge">CRYPTO</span></li>
                    <li>Device Fingerprinting <span class="tech-badge">SECURITY</span></li>
                    <li>Rate Limiting <span class="tech-badge">PROTECTION</span></li>
                </ul>
            </div>
            
            <div class="tech-category">
                <h3>☁️ Infrastructure</h3>
                <ul class="tech-list">
                    <li>Kubernetes <span class="tech-badge">ORCHESTRATION</span></li>
                    <li>Docker <span class="tech-badge">CONTAINERS</span></li>
                    <li>AWS/GCP <span class="tech-badge">CLOUD</span></li>
                    <li>CloudFlare CDN <span class="tech-badge">DELIVERY</span></li>
                    <li>Prometheus <span class="tech-badge">MONITORING</span></li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Add interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const components = document.querySelectorAll('.component');
            
            components.forEach((component, index) => {
                component.style.animationDelay = `${index * 0.1}s`;
                component.style.animation = 'fadeInUp 0.6s ease forwards';
            });
            
            // Add hover effects for layers
            const layers = document.querySelectorAll('.layer');
            layers.forEach(layer => {
                layer.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                layer.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
        
        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .component {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
        
        console.log('Instagram + System Architecture loaded! 🏗️');
    </script>
</body>
</html>
