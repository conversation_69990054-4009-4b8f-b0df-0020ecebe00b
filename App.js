import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <Text style={styles.title}>Instagram +</Text>
      <Text style={styles.subtitle}>Instagram with Free Verification & Advanced Security</Text>
      <Text style={styles.description}>
        📱 Enhanced Instagram experience with free verification badges
      </Text>
      <Text style={styles.description}>
        🔐 Enterprise-grade security features
      </Text>
      <Text style={styles.description}>
        ✅ Free verification for all authentic users
      </Text>
      <Text style={styles.description}>
        🛡️ Advanced biometric authentication
      </Text>
      <Text style={styles.description}>
        🌍 Beautiful African-inspired design
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#228B22',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
    fontWeight: '500',
  },
  description: {
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
    lineHeight: 24,
  },
});
