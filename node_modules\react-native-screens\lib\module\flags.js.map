{"version": 3, "names": ["RNS_CONTROLLED_BOTTOM_TABS_DEFAULT", "compatibilityFlags", "isNewBackTitleImplementation", "usesHeaderFlexboxImplementation", "_featureFlags", "experiment", "controlledBottomTabs", "stable", "featureFlags", "value", "console", "error"], "sourceRoot": "../../src", "sources": ["flags.ts"], "mappings": "AAAA,MAAMA,kCAAkC,GAAG,IAAI;;AAE/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChC;AACF;AACA;AACA;AACA;AACA;EACEC,4BAA4B,EAAE,IAAI;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,+BAA+B,EAAE;AACnC,CAAU;AAEV,MAAMC,aAAa,GAAG;EACpBC,UAAU,EAAE;IACVC,oBAAoB,EAAEN;EACxB,CAAC;EACDO,MAAM,EAAE,CAAC;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1B;AACF;AACA;EACEH,UAAU,EAAE;IACV,IAAIC,oBAAoBA,CAAA,EAAG;MACzB,OAAOF,aAAa,CAACC,UAAU,CAACC,oBAAoB;IACtD,CAAC;IACD,IAAIA,oBAAoBA,CAACG,KAAc,EAAE;MACvC,IACEA,KAAK,KAAKL,aAAa,CAACC,UAAU,CAACC,oBAAoB,IACvDF,aAAa,CAACC,UAAU,CAACC,oBAAoB,KAC3CN,kCAAkC,EACpC;QACAU,OAAO,CAACC,KAAK,CACX,iHACF,CAAC;MACH;MACAP,aAAa,CAACC,UAAU,CAACC,oBAAoB,GAAGG,KAAK;IACvD;EACF,CAAC;EACD;AACF;AACA;EACEF,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeC,YAAY", "ignoreList": []}