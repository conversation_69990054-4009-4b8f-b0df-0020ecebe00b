{"version": 3, "names": ["React", "StyleSheet", "ScreenStackHostNativeComponent", "ScreenStackHost", "children", "createElement", "style", "styles", "container", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["components/gamma/ScreenStackHost.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,cAAc;AAGzC,OAAOC,8BAA8B,MAAM,mDAAmD;AAU9F;AACA;AACA;AACA,SAASC,eAAeA,CAAC;EAAEC;AAA+B,CAAC,EAAE;EAC3D,oBACEJ,KAAA,CAAAK,aAAA,CAACH,8BAA8B;IAACI,KAAK,EAAEC,MAAM,CAACC;EAAU,GACrDJ,QAC6B,CAAC;AAErC;AAEA,MAAMG,MAAM,GAAGN,UAAU,CAACQ,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAeP,eAAe", "ignoreList": []}